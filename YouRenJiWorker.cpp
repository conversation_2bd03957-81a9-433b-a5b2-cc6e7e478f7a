#include "YouRenJiWorker.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonParseError>
#include <QDebug>
#include <QCoreApplication>
#include <QtConcurrent>
#include <QThread>
#include <QDateTime>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QElapsedTimer>

YouRenJiWorker::YouRenJiWorker(QObject *parent)
    : QObject(parent)
    , isRunning(false)
    , serialPortOpen(false)
    , messageCount(0)
    , dataBytesProcessed(0)
    , messageSamplingRate(1)  // 默认处理所有消息
    , logSamplingRate(1)      // 默认记录所有日志
    , messageCounter(0)
    , logCounter(0)
    , bufferFlushTimer(nullptr)
    , zaiheUdpSend(nullptr)
    , uavUdpRecv(nullptr)
    , serialPort(nullptr)
    , serialErrorCount(0)
    , udpErrorCount(0)
    , dataValidationErrors(0)
{
    // 注册元类型，以支持跨线程信号槽
    qRegisterMetaType<QAbstractSocket::SocketError>("QAbstractSocket::SocketError");
    qRegisterMetaType<QAbstractSocket::SocketState>("QAbstractSocket::SocketState");
    qRegisterMetaType<QSerialPort::SerialPortError>("QSerialPort::SerialPortError");

    // 初始化性能计时器
    performanceTimer.start();
    lastErrorTime.start();

    // 光电赋值
    //    eo_info.control_type = 1;
    //    eo_info.control_order = 1;
    //    eo_info.az_ang = 1;
    //    eo_info.ei_ang = 1;
    //    eo_info.ccd_view = 1;
    //    eo_info.ir_view = 1;
    //    eo_info.photo_view = 1;
    //    eo_info.pod_status_1.spare = 1;
    //    eo_info.pod_status_1.eo_status = 1;
    //    eo_info.pod_status_1.camera_status = 1;
    //    eo_info.pod_status_1.ir_sensor_status = 1;
    //    eo_info.pod_status_1.tv_sensor_status = 1;
    //    eo_info.pod_status_1.servo_unit_status = 1;
    //    eo_info.pod_status_1.image_track_status = 1;
    //    eo_info.pod_status_1.control_unit_status = 1;
    //    eo_info.pod_status_two.spare = 1;
    //    eo_info.pod_status_two.in_pitch_status = 1;
    //    eo_info.pod_status_two.out_pitch_status = 1;
    //    eo_info.pod_status_two.pitch_top_status = 1;
    //    eo_info.pod_status_two.in_postion_status = 1;
    //    eo_info.pod_status_two.out_postion_status = 1;
    //    eo_info.pod_status_two.postion_top_status = 1;
    //    eo_info.pod_status_two.sensor_control_status = 1;
    //    eo_info.pod_work_status.ir_statu = 1;
    //    eo_info.pod_work_status.camare_statu = 1;
    //    eo_info.pod_work_status.ir_big_statu = 1;
    //    eo_info.pod_work_status.ir_mix_statu = 1;
    //    eo_info.pod_work_status.track_num_statu = 1;
    //    eo_info.pod_work_status.ir_enhance_statu = 1;
    //    eo_info.pod_work_status.image_track_statu = 1;
    //    eo_info.pod_work_status.ir_trans_fog_statu = 1;
    //    eo_info.pod_work_status.ir_right_image_statu = 1;
    //    eo_info.pod_work_status_Two.spare = 1;
    //    eo_info.pod_work_status_Two.ir_main_path = 1;
    //    eo_info.pod_work_status_Two.eo_work_status = 1;
    //    eo_info.pod_work_status_Two.search_condition = 1;
    //    eo_info.pod_work_status_Two.class_width_statu = 1;
    //    eo_info.pod_work_status_Two.image_track_statu = 1;
    //    eo_info.pod_work_status_Two.stick_sensitive_statu = 1;
    memset(&eo_info,0,sizeof (eo_info));
}

YouRenJiWorker::~YouRenJiWorker()
{
    stop();

    if (bufferFlushTimer) {
        bufferFlushTimer->stop();
        delete bufferFlushTimer;
        bufferFlushTimer = nullptr;
    }

    // 清理Socket对象
    delete zaiheUdpSend;
    delete uavUdpRecv;

    // 清理SerialPort对象
    if (serialPort) {
        if (serialPort->isOpen()) {
            serialPort->close();
        }
        delete serialPort;
        serialPort = nullptr;
    }
}

// 辅助方法，添加时间戳并发送日志
void YouRenJiWorker::emitLogMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    emit messageReceived(QString("[%1] %2").arg(timestamp).arg(message));
}

// 辅助方法，添加时间戳并发送错误
void YouRenJiWorker::emitErrorMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    emit errorOccurred(QString("[%1] %2").arg(timestamp).arg(message));
}

// 初始化UDP Socket
void YouRenJiWorker::initUdpSockets()
{
    // 创建Socket对象
    zaiheUdpSend = new QUdpSocket(this);
    uavUdpRecv = new QUdpSocket(this);

    // 设置缓冲区大小以处理高吞吐量
    zaiheUdpSend->setSocketOption(QAbstractSocket::SendBufferSizeSocketOption, UDP_BUFFER_SIZE);
    uavUdpRecv->setSocketOption(QAbstractSocket::ReceiveBufferSizeSocketOption, UDP_BUFFER_SIZE);

    // 设置socket选项以优化性能
    zaiheUdpSend->setSocketOption(QAbstractSocket::LowDelayOption, 1);
    uavUdpRecv->setSocketOption(QAbstractSocket::LowDelayOption, 1);

    // 连接信号 - 使用Qt::QueuedConnection确保线程安全
    connect(uavUdpRecv, &QUdpSocket::readyRead, this, &YouRenJiWorker::processUavUdpData, Qt::QueuedConnection);
    // 保留自定义信号作为备用触发机制
    connect(this, &YouRenJiWorker::startUDP, this, &YouRenJiWorker::processUavUdpData, Qt::QueuedConnection);

    // 绑定UAV MultiCast接收端口并加入组
    try {
        if (!uavUdpRecv->bind(QHostAddress::AnyIPv4, uavRecvPort.toUShort(), QUdpSocket::ShareAddress | QUdpSocket::ReuseAddressHint)) {
            throw std::runtime_error(uavUdpRecv->errorString().toStdString());
        }

        if (!uavUdpRecv->joinMulticastGroup(QHostAddress(uavRecvIP))) {
            throw std::runtime_error(uavUdpRecv->errorString().toStdString());
        }

        emitLogMessage(QString("[系统] 无人机接收组播绑定成功: %1:%2").arg(uavRecvIP).arg(uavRecvPort));
    } catch (const std::exception& e) {
        emitErrorMessage(QString("[错误] 无人机接收组播绑定失败: %1").arg(e.what()));
    }
}

// 初始化串口
void YouRenJiWorker::initSerialPort()
{
    if (serialPort == nullptr) {
        serialPort = new QSerialPort(this);

        // 连接信号
        connect(serialPort, &QSerialPort::errorOccurred, this, [this](QSerialPort::SerialPortError error) {
            if (error != QSerialPort::NoError) {
                emit serialErrorOccurred(serialPort->errorString());
                // 如果是严重错误，关闭串口
                if (error != QSerialPort::TimeoutError && error != QSerialPort::NotOpenError) {
                    stopSerialPort();
                }
            }
        });

        // 连接readyRead信号到处理串口数据的方法 - 使用Qt::QueuedConnection确保线程安全
        connect(serialPort, &QSerialPort::readyRead, this, &YouRenJiWorker::processSerialData, Qt::QueuedConnection);
        // 保留自定义信号作为备用触发机制
        connect(this, &YouRenJiWorker::startSerial, this, &YouRenJiWorker::processSerialData, Qt::QueuedConnection);
    }
}

void YouRenJiWorker::setAddresses(
        const QString &zaiheSendIP, const QString &zaiheSendPort,
        const QString &uavRecvIP, const QString &uavRecvPort)
{
    QMutexLocker locker(&stateMutex);
    this->zaiheSendIP = zaiheSendIP;
    this->zaiheSendPort = zaiheSendPort;
    this->uavRecvIP = uavRecvIP;
    this->uavRecvPort = uavRecvPort;
}

void YouRenJiWorker::setSerialPortSettings(
        const QString &portName,
        int baudRate,
        QSerialPort::DataBits dataBits,
        QSerialPort::Parity parity,
        QSerialPort::StopBits stopBits)
{
    QMutexLocker locker(&stateMutex);
    this->serialPortName = portName;
    this->serialBaudRate = baudRate;
    this->serialDataBits = dataBits;
    this->serialParity = parity;
    this->serialStopBits = stopBits;

    emitLogMessage(QString("[系统] 串口设置已更新: %1, %2波特, %3数据位, %4校验位, %5停止位")
                   .arg(portName)
                   .arg(baudRate)
                   .arg(dataBits)
                   .arg(parity)
                   .arg(stopBits));
}

void YouRenJiWorker::setMessageSamplingRate(int rate)
{
    QMutexLocker locker(&stateMutex);
    messageSamplingRate = qMax(1, rate); // 确保至少是1
    emitLogMessage(QString("[系统] 消息采样率已设置为 1/%1").arg(messageSamplingRate));
}

void YouRenJiWorker::setLogSamplingRate(int rate)
{
    QMutexLocker locker(&stateMutex);
    logSamplingRate = qMax(1, rate); // 确保至少是1
    emitLogMessage(QString("[系统] 日志采样率已设置为 1/%1").arg(logSamplingRate));
}

bool YouRenJiWorker::shouldLogMessage()
{
    return (++logCounter % logSamplingRate) == 0;
}

void YouRenJiWorker::start()
{
    if (!isRunning) {
        QMutexLocker locker(&stateMutex);
        serialAlign = false;
        isRunning = true;
        emitLogMessage("[系统] 正在启动消息处理系统...");
        // 初始化UDP Sockets
        initUdpSockets();
        // 初始化定时器
        if (!bufferFlushTimer) {
            bufferFlushTimer = new QTimer(this);
            bufferFlushTimer->setInterval(BUFFER_FLUSH_INTERVAL_MS);
            // 使用Qt::QueuedConnection确保线程安全
            connect(bufferFlushTimer, &QTimer::timeout, this, &YouRenJiWorker::flushBufferedData, Qt::QueuedConnection);
            // 保留自定义信号作为备用触发机制
            connect(this, &YouRenJiWorker::startBuffer, this, &YouRenJiWorker::flushBufferedData, Qt::QueuedConnection);
        }

        //        createSubThread(&fun1, &helper1, "fun1");
        //        createSubThread(&fun2, &helper2, "fun2");
        //        createSubThread(&fun3, &helper3, "fun3");
        //        QMetaObject::invokeMethod(helper1, "trigger", Qt::QueuedConnection);
        //        QMetaObject::invokeMethod(helper2, "trigger", Qt::QueuedConnection);
        //        QMetaObject::invokeMethod(helper3, "trigger", Qt::QueuedConnection);



        // 设置线程优先级
        QThread::currentThread()->setPriority(QThread::HighPriority);

        // 重置计数器
        messageCount = 0;
        dataBytesProcessed = 0;

        bufferFlushTimer->start();

        emit connectionStatusChanged(true);
        emitLogMessage("[系统] 消息处理系统已启动");
    }
}

void YouRenJiWorker::stop()
{
    if (isRunning) {
        QMutexLocker locker(&stateMutex);
        isRunning = false;
        serialAlign = false;
        emitLogMessage("[系统] 正在停止消息处理系统...");
        stopSubThreads();
        if (bufferFlushTimer) {
            bufferFlushTimer->stop();
        }

        // 清空所有缓冲区
        QMutexLocker bufferLocker(&bufferMutex);
        serialDataQueue.clear();

        emit connectionStatusChanged(false);
        emitLogMessage("[系统] 消息处理系统已停止");
    }
}

void YouRenJiWorker::startSerialPort()
{
    QMutexLocker locker(&stateMutex);

    // 如果已经打开，先关闭
    if (serialPort && serialPort->isOpen()) {
        serialPort->close();
        emitLogMessage("[系统] 关闭之前的串口连接");
    }

    // 如果没有串口对象，创建一个
    if (!serialPort) {
        initSerialPort();
    }

    // 配置串口参数
    serialPort->setPortName(serialPortName);
    serialPort->setBaudRate(serialBaudRate);
    serialPort->setDataBits(serialDataBits);
    serialPort->setParity(serialParity);
    serialPort->setStopBits(serialStopBits);
    serialPort->setFlowControl(QSerialPort::NoFlowControl);

    // 设置串口缓冲区大小和超时
    serialPort->setReadBufferSize(MAX_SERIAL_BUFFER_SIZE);
    serialPort->setSettingsRestoredOnClose(false);

    // 尝试打开串口
    if (serialPort->open(QIODevice::ReadWrite)) {
        serialPortOpen = true;
        emit serialPortStatusChanged(true);
        emitLogMessage(QString("[系统] 串口 %1 已成功打开").arg(serialPortName));
    } else {
        serialPortOpen = false;
        emit serialPortStatusChanged(false);
        emitErrorMessage(QString("[错误] 无法打开串口 %1: %2").arg(serialPortName).arg(serialPort->errorString()));
    }
}

void YouRenJiWorker::stopSerialPort()
{
    QMutexLocker locker(&stateMutex);

    if (serialPort && serialPort->isOpen()) {
        serialPort->close();
        serialPortOpen = false;
        emit serialPortStatusChanged(false);
        emitLogMessage(QString("[系统] 串口 %1 已关闭").arg(serialPortName));
    }
}

void YouRenJiWorker::flushBufferedData()
{
    //    qDebug("flushBufferedData");
    QMutexLocker locker(&bufferMutex);

    // 处理多个队列项以提高吞吐量，避免传输中断
    int processedCount = 0;
    const int maxProcessPerCall = 10; // 每次最多处理10个数据包

    while (!serialDataQueue.isEmpty() && serialPortOpen && serialPort && serialPort->isOpen() && processedCount < maxProcessPerCall) {
        QByteArray data = serialDataQueue.dequeue();
        processedCount++;

        if(data.size() == 20){
            // 处理20字节的EO信息数据
            memcpy(&eo_info, data.data(), sizeof (eo_info));

            // 直接发送20字节数据
            qint64 bytesWritten = serialPort->write(data);
            if (bytesWritten != data.size()) {
                emitErrorMessage(QString("[错误] 20字节数据发送不完整: 期望%1，实际%2").arg(data.size()).arg(bytesWritten));
                continue;
            }
            serialPort->flush();

        } else if (data.size() == 256){
            // 处理256字节数据的分块发送
            int chunkSize = 64; // 每份 64 字节
            int totalChunks = data.size() / chunkSize; // 数据被拆分成多少个完整块
            if (data.size() % chunkSize != 0) {
                totalChunks += 1; // 如果有剩余字节，需要额外添加一个块
            }

            bool transmissionSuccess = true;
            // 按 64 字节分块发送数据
            for (int i = 0; i < totalChunks && transmissionSuccess; ++i) {
                int offset = i * chunkSize;
                int currentChunkSize = (i == totalChunks - 1) ? data.size() - offset : chunkSize; // 最后一块数据可能不足 64 字节

                QByteArray chunkData = data.mid(offset, currentChunkSize); // 获取当前分块数据

                // 特殊处理第4块数据（索引3）
                if(i == 3 && chunkData.size() >= 64){
                    chunkData[3] = 0x53;
                    memcpy((chunkData.data() + 5),&eo_info,sizeof (eo_info));
                    short j = 0;
                    for (int k = 3; k < 62; k++) {
                        j += static_cast<unsigned char>(chunkData[k]);
                    }
                    chunkData[63] = static_cast<char>(j & 0xFF);
                }

                // 写入数据到串口
                qint64 bytesWritten = serialPort->write(chunkData);
                if (bytesWritten != currentChunkSize) {
                    emitErrorMessage(QString("[错误] 串口数据块%1发送不完整: 期望%2字节，实际%3字节，错误:%4")
                                     .arg(i).arg(currentChunkSize).arg(bytesWritten).arg(serialPort->errorString()));
                    transmissionSuccess = false;
                    break;
                }

                // 确保数据立即发送
                if (!serialPort->flush()) {
                    emitErrorMessage(QString("[警告] 串口数据块%1刷新失败").arg(i));
                }

                // 添加小延迟以确保数据传输稳定性
                QThread::usleep(100); // 100微秒延迟
            }

            if (!transmissionSuccess) {
                // 如果传输失败，打印日志
                emitErrorMessage("[错误] 256字节数据传输失败，数据可能丢失");
            }

        } else {
            // 处理其他大小的数据包
            qint64 bytesWritten = serialPort->write(data);
            if (bytesWritten != data.size()) {
                emitErrorMessage(QString("[错误] %1字节数据发送不完整: 期望%2，实际%3")
                                 .arg(data.size()).arg(data.size()).arg(bytesWritten));
                continue;
            }
            serialPort->flush();
        }
    }

    // 动态调整定时器间隔以优化性能
    if (!serialDataQueue.isEmpty() && serialPortOpen) {
        // 队列非空时，使用快速刷新间隔
        if (bufferFlushTimer && bufferFlushTimer->interval() > BUFFER_FLUSH_INTERVAL_FAST_MS) {
            bufferFlushTimer->setInterval(BUFFER_FLUSH_INTERVAL_FAST_MS);
        }
    } else {
        // 队列为空时，恢复正常间隔以节省CPU
        if (bufferFlushTimer && bufferFlushTimer->interval() < BUFFER_FLUSH_INTERVAL_MS) {
            bufferFlushTimer->setInterval(BUFFER_FLUSH_INTERVAL_MS);
        }
    }
}

// 处理串口接收到的数据并转发到载荷组播
void YouRenJiWorker::processSerialData()
{
    //    qDebug("processSerialData");
    if (!isRunning || !serialPort || !serialPort->isOpen()) return;

    // 处理所有可用的数据，避免数据丢失
    while (serialPort->bytesAvailable() > 0) {
        QByteArray serialData, tmpData;
        uint8_t type, type1, cmdType, cmdCode, cmdData;
        int alignCnt = 128;

        if(!serialAlign){
            // 改进的对齐逻辑，确保不丢失数据
            do{
                alignCnt--;
                // 等待数据可用
                if (serialPort->bytesAvailable() < 1) {
                    serialPort->waitForReadyRead(SERIAL_READ_TIMEOUT_MS / 5); // 等待最多10ms
                    if (serialPort->bytesAvailable() < 1) break;
                }

                header1 = serialPort->read(1);
                if(header1.isEmpty()) break;

                if(static_cast<unsigned char>(header1[0]) == 0xeb){
                    // 等待第二个字节
                    if (serialPort->bytesAvailable() < 1) {
                        serialPort->waitForReadyRead(SERIAL_READ_TIMEOUT_MS / 5);
                    }
                    if (serialPort->bytesAvailable() >= 1) {
                        header2 = serialPort->read(1);
                        if(!header2.isEmpty() && static_cast<unsigned char>(header2[0]) == 0x90){
                            serialAlign = true;
                            break;
                        }
                    }
                }
                else if(static_cast<unsigned char>(header1[0]) == 0xaa){
                    // 等待第二个字节
                    if (serialPort->bytesAvailable() < 1) {
                        serialPort->waitForReadyRead(SERIAL_READ_TIMEOUT_MS / 5);
                    }
                    if (serialPort->bytesAvailable() >= 1) {
                        header2 = serialPort->read(1);
                        if(!header2.isEmpty() && static_cast<unsigned char>(header2[0]) == 0x55){
                            serialAlign = true;
                            break;
                        }
                    }
                }
                if (alignCnt == 0) break;
            } while(1);

            if (serialAlign) {
                // 等待剩余的62字节数据
                int remainingBytes = 62;
                while (remainingBytes > 0 && serialPort->bytesAvailable() < remainingBytes) {
                    serialPort->waitForReadyRead(SERIAL_READ_TIMEOUT_MS); // 等待更多数据
                    if (serialPort->bytesAvailable() == 0) break; // 避免无限等待
                }

                tmpData = serialPort->read(remainingBytes);
                serialData.append(header1);
                serialData.append(header2);
                serialData.append(tmpData);
            } else {
                // 对齐失败，跳过处理
                continue;
            }
        } else {
            // 已对齐，读取固定长度数据
            int expectedBytes = 64;
            while (serialPort->bytesAvailable() < expectedBytes) {
                serialPort->waitForReadyRead(SERIAL_READ_TIMEOUT_MS);
                if (serialPort->bytesAvailable() == 0) return; // 没有更多数据
            }
            serialData = serialPort->read(expectedBytes);
        }

        // 验证数据完整性
        if (serialData.size() < 64) {
            serialErrorCount++;
            emitErrorMessage(QString("[警告] 串口数据不完整，期望64字节，实际%1字节").arg(serialData.size()));
            serialAlign = false; // 重新对齐
            continue;
        }

        // 验证数据包格式
        if (!validateSerialPacket(serialData)) {
            serialErrorCount++;
            emitErrorMessage(QString("[警告] 串口数据包格式无效，重新对齐"));
            serialAlign = false; // 重新对齐
            continue;
        }
        // 处理数据包内容
        if(static_cast<unsigned char>(serialData[0]) == 0xeb){
            type = static_cast<unsigned char>(serialData[5]);
            type1 = static_cast<unsigned char>(serialData[3]);
            if (type == 0xA3 && type1 == 0x5A) {
                        uint8_t tmp = static_cast<unsigned char>(serialData[17]);
                        uint8_t tmp1 = static_cast<unsigned char>(serialData[18]);
                        uint8_t tmp2 = static_cast<unsigned char>(serialData[19]);
                        if(tmp==0x33 && tmp1==0x33 && tmp2 == 0x33){
                            qDebug() << "received it";
                        }
                // 确保UDP发送成功
                qint64 bytesSent = uavUdpRecv->writeDatagram(serialData, QHostAddress("**********"), 7101);
                if (bytesSent != serialData.size()) {
                    emitErrorMessage(QString("[错误] UDP数据发送不完整: 期望%1字节，实际发送%2字节").arg(serialData.size()).arg(bytesSent));
                }
            }

        } else if(static_cast<unsigned char>(serialData[0]) == 0xaa){
        cmdType = static_cast<unsigned char>(serialData[9]);
        cmdCode = static_cast<unsigned char>(serialData[10]);
        cmdData = static_cast<unsigned char>(serialData[11]);
        if (cmdType == 0x20 && cmdCode == 0x30) {
            emitLogMessage(QString("手控指令接收成功"));
            send_YK(CONTROL, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x32)
        {
            emitLogMessage(QString("自检指令接收成功"));
            send_YK(SELFINSPECTION, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x34)
        {
            emitLogMessage(QString("归零指令接收成功"));
            send_YK(RETURNZERO, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0xB4)
        {
            emitLogMessage(QString("拍照指令接收成功"));
            send_YK(TAKEPHOTO,0,0,0,0,0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x40)
        {
            emitLogMessage(QString("相机垂直下视指令接收成功"));
            send_YK(RETURNVIEWSTATE, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x7A && cmdData == 0x01)
        {
            emitLogMessage(QString("视景上移指令接收成功"));
            send_YK(VIEWUP,0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x7A && cmdData == 0xFF)
        {
            emitLogMessage(QString("视景下移指令接收成功"));
            send_YK(VIEWDOWN,0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x7C && cmdData == 0xFF)
        {
            emitLogMessage(QString("视景左移指令接收成功"));
            send_YK(VIEWLEFT,0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x7C && cmdData == 0x01)
        {
            emitLogMessage(QString("视景右移指令接收成功"));
            send_YK(VIEWRIGHT,0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x42)
        {
            emitLogMessage(QString("跟踪指令接收成功"));
            send_YK(TARGETTRACK, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x44)
        {
            emitLogMessage(QString("位置指令接收成功"));
            send_YK(POSITIONTRACK, 0, 0, 0, 0, 0);
        }
        else if (cmdType == 0x20 && cmdCode == 0x46)
        {
            if (cmdData == 0x00)
            {
                send_YK(SWITCHLOAD, 0, 0, 0, 0, 0);//0：电视
            }
            else if (cmdData == 0x01)
            {
                send_YK(SWITCHLOAD, 1, 0, 0, 0, 0);//1：红外
            }
            emitLogMessage(QString("跟踪监视主通道指令接收成功"));
        }
        else if (cmdType == 0x20 && cmdCode == 0x80)
        {
            if (cmdData == 0x00)
            {
                send_YK(CCDCHANGEZOOM, 0, 0, 0, 0, 0);//0：电视变焦停
            }
            else if (cmdData == 0x01)
            {
                send_YK(CCDCHANGEZOOM, 1, 0, 0, 0, 0);//1：电视变焦+
            }
            else if (cmdData == 0xFF)
            {
                send_YK(CCDCHANGEZOOM, 2, 0, 0, 0, 0);//1：电视变焦-
            }
            emitLogMessage(QString("电视变焦指令接收成功"));
        }
        else if (cmdType == 0x20 && cmdCode == 0x98)
        {
            if (cmdData == 0x00)
            {
                send_YK(IRCHANGEZOOM, 0, 0, 0, 0, 0);//0：电视变焦停
            }
            else if (cmdData == 0x01)
            {
                send_YK(IRCHANGEZOOM, 1, 0, 0, 0, 0);//1：电视变焦+
            }
            else if (cmdData == 0xFF)
            {
                send_YK(IRCHANGEZOOM, 2, 0, 0, 0, 0);//1：电视变焦-
            }
            emitLogMessage(QString("红外变焦指令接收成功"));
        }
        else if (cmdType == 0x20 && cmdCode == 0x98)
        {
            if (cmdData == 0x01)
            {
                send_YK(CHANGESENSITIVITY, 0, 0, 0, 0, 0);//0：灵敏度+（共6个挡位）
            }
            else if (cmdData == 0xFF)
            {
                send_YK(CHANGESENSITIVITY, 1, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
            }
            emitLogMessage(QString("灵敏度指令接收成功"));
        }
        else if (cmdType == 0x20 && cmdCode == 0x52)
        {
            emitLogMessage(QString("光电载荷关机指令接收成功"));
            send_YK(CCDCLOSE, 0, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
        else if (cmdType == 0x20 && cmdCode == 0x10)
        {
            emitLogMessage(QString("光电载荷唤醒指令接收成功"));
            send_YK(CCDAWAKEN, 0, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
        else if (cmdType == 0x20 && cmdCode == 0x90)
        {
            emitLogMessage(QString("红外电源开指令接收成功"));
            send_YK(IROPEN, 0, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
        else if (cmdType == 0x20 && cmdCode == 0x92)
        {
            emitLogMessage(QString("红外电源关指令接收成功"));
            send_YK(IRCLOSE, 0, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
        else if (cmdType == 0x20 && cmdCode == 0xB6)
        {
            emitLogMessage(QString("连续拍照指令接收成功"));
            send_YK(IRCLOSE, cmdData, 0, 0, 0, 0);//1：灵敏度-（共6个挡位）
        }
        }

        // 更新统计信息
        messageCount++;
        dataBytesProcessed += serialData.size();

        // 记录日志
        if (shouldLogMessage()) {
            QString hexData = serialData.toHex();
            // 限制日志长度以避免UI过载
            if (hexData.length() > 200) {
                hexData = hexData.left(197) + "...";
            }
            emitLogMessage(QString("[串口=>载荷组播] <span class='serial'>%1</span>").arg(hexData));
        }
    } // 结束while循环
}

void YouRenJiWorker::send_YK(const int& cmdNum,const double&cmdValue1, const double& cmdValue2, const double& cmdValue3, const double& cmdValue4, const double& cmdValue5)
{
    // 构建JSON数据
    QJsonObject json;

    json["MESSAGETYPE"] = "YKCmd";
    json["cmdType"] = cmdNum;
    json["cmdValue1"] = cmdValue1;
    json["cmdValue2"] = cmdValue2;
    json["cmdValue3"] = cmdValue3;
    json["cmdValue4"] = cmdValue4;
    json["cmdValue5"] = cmdValue5;

    QJsonDocument doc(json);
    QByteArray jsonData = doc.toJson();

    // 发送JSON数据
    zaiheUdpSend->writeDatagram(jsonData, QHostAddress("*********"), 6604);
}

// 处理无人机UDP数据
void YouRenJiWorker::processUavUdpData()
{
    //    qDebug("processUavUdpData");
    if (!isRunning || !serialPortOpen) return;

    // 处理所有待处理的数据包，避免数据丢失
    while (uavUdpRecv->hasPendingDatagrams()) {
        // 读取UDP数据包
        QByteArray datagram;
        datagram.resize(uavUdpRecv->pendingDatagramSize());
        QHostAddress sender;
        quint16 senderPort;

        qint64 bytesRead = uavUdpRecv->readDatagram(datagram.data(), datagram.size(), &sender, &senderPort);
        if (bytesRead <= 0) {
            udpErrorCount++;
            emitErrorMessage("[错误] UDP数据读取失败");
            continue;
        }

        // 验证UDP数据包大小合理性
        if (bytesRead > 65536) { // 64KB限制
            udpErrorCount++;
            emitErrorMessage(QString("[警告] UDP数据包异常大小: %1字节").arg(bytesRead));
            continue;
        }

        // 调整数据大小为实际读取的字节数
        datagram.resize(bytesRead);

        // 更新统计信息
        messageCount++;
        dataBytesProcessed += datagram.size();

        // 应用消息采样（仅用于日志记录，不影响数据处理）
        bool shouldProcess = ((++messageCounter % messageSamplingRate) == 0);

        // 通过串口发送数据（所有数据都要发送，不受采样影响）
        sendToSerial(datagram);

        // 记录日志（受采样率影响）
        if (shouldProcess && shouldLogMessage()) {
            QString hexData = datagram.toHex();
            // 限制日志长度以避免UI过载
            if (hexData.length() > 200) {
                hexData = hexData.left(197) + "...";
            }
            emitLogMessage(QString("[无人机=>串口] <span class='serial'>%1</span>").arg(hexData));
        }
    }

    // 触发串口数据处理（如果需要）
    emit startSerial();
}

// 发送数据到串口
void YouRenJiWorker::sendToSerial(const QByteArray &data)
{
    if (!serialPortOpen || !serialPort || !serialPort->isOpen()) {
        if (shouldLogMessage()) {
            emitErrorMessage("[错误] 尝试发送数据到未打开的串口");
        }
        return;
    }

    QMutexLocker locker(&bufferMutex);

    // 如果缓冲区已满，移除最早的项目
    if (serialDataQueue.size() >= MAX_SERIAL_BUFFER_SIZE) {
        serialDataQueue.dequeue();
        if (shouldLogMessage()) {
            emitErrorMessage("[警告] 串口发送缓冲区已满，丢弃最早的数据");
        }
    }

    // 将数据加入缓冲区队列
    serialDataQueue.enqueue(data);
}

// 验证串口数据包完整性
bool YouRenJiWorker::validateSerialPacket(const QByteArray &data)
{
    if (data.size() < 4) {
        dataValidationErrors++;
        return false;
    }

    // 检查数据包头
    uint8_t header1 = static_cast<uint8_t>(data[0]);
    uint8_t header2 = static_cast<uint8_t>(data[1]);

    // 验证已知的数据包格式
    if ((header1 == 0xeb && header2 == 0x90) || (header1 == 0xaa && header2 == 0x55)) {
        // 对于64字节的数据包，可以进行更详细的验证
        if (data.size() == 64) {
            // 可以添加更多的数据包特定验证逻辑
            return true;
        }
        return true;
    }

    dataValidationErrors++;
    return false;
}

// 计算校验和
uint8_t YouRenJiWorker::calculateChecksum(const QByteArray &data, int start, int end)
{
    if (start < 0 || end >= data.size() || start > end) {
        return 0;
    }

    uint16_t sum = 0;
    for (int i = start; i <= end; i++) {
        sum += static_cast<uint8_t>(data[i]);
    }

    return static_cast<uint8_t>(sum & 0xFF);
}
