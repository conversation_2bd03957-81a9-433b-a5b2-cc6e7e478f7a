﻿#include "QingBao.h"
#include "ui_QingBao.h"
#include "QingBaoWorker.h"
#include <QDebug>
#include <QRegExp>
#include <QRegExpValidator>
#include <QScrollBar>
#include <QSpinBox>
#include <QLabel>
#include <QVBoxLayout>
#include <QTimer>
#include <QMutex>
#include <QTextCursor>
#include <QDateTime>
#include <QFontDatabase>
#include <QMetaType>
#include <QFileDialog>
#include <QGroupBox>
#include <QStyleFactory>
#include <QApplication>
#include <QPalette>
#include <QMessageBox>
#include <QStyle>

QingBao::<PERSON><PERSON><PERSON>(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::Qing<PERSON>ao),
    worker(new QingBaoWorker),
    logUpdateTimer(nullptr),
    connectState(false)
{
    // 注册元类型，以支持跨线程信号槽
    qRegisterMetaType<QAbstractSocket::SocketError>("QAbstractSocket::SocketError");
    qRegisterMetaType<QAbstractSocket::SocketState>("QAbstractSocket::SocketState");

    ui->setupUi(this);

    // 设置窗口图标
    setWindowIcon(QApplication::style()->standardIcon(QStyle::SP_ComputerIcon));

    // 设置日志更新定时器
    logUpdateTimer = new QTimer(this);
    logUpdateTimer->setInterval(LOG_UPDATE_INTERVAL);
    connect(logUpdateTimer, &QTimer::timeout, this, &QingBao::updateLogDisplay);
    logUpdateTimer->start();

    // 设置文本浏览器属性为启用富文本
    ui->qingbaoMsg->setAcceptRichText(true);
    ui->qingbaoMsg->document()->setMaximumBlockCount(10000); // 限制最大行数，防止内存溢出

    // 设置等宽字体，使日志显示更整齐
    QFont logFont = QFontDatabase::systemFont(QFontDatabase::FixedFont);
    logFont.setPointSize(9);
    ui->qingbaoMsg->setFont(logFont);

    // 设置CSS样式，美化日志显示
    ui->qingbaoMsg->document()->setDefaultStyleSheet(
        "body { color: #333; }"
        ".system { color: #008800; }"
        ".error { color: #cc0000; font-weight: bold; }"
        ".data { color: #0000aa; }"
        ".timestamp { color: #888; font-weight: normal; }"
        ".highlight { background-color: #ffffcc; }"
        ".file { color: #9900cc; }"
        ".send { color: #ff6600; }"
    );

    // 准备工作线程，但不立即启动
    worker->moveToThread(&workerThread);

    // 连接信号和槽
    connect(&workerThread, &QThread::finished, worker, &QObject::deleteLater);
    connect(worker, &QingBaoWorker::messageReceived, this, &QingBao::handleMessage, Qt::QueuedConnection);
    connect(worker, &QingBaoWorker::errorOccurred, this, &QingBao::handleError, Qt::QueuedConnection);
    connect(worker, &QingBaoWorker::connectionStatusChanged, this, &QingBao::updateConnectionStatus, Qt::QueuedConnection);
    connect(worker, &QingBaoWorker::performanceUpdated, this, &QingBao::updateProcessingStats, Qt::QueuedConnection);

    // 连接按钮点击信号
    connect(ui->qingbao, &QPushButton::clicked, this, &QingBao::onConnect);

    // 创建正则表达式验证器
    QRegExp ipPortRegExp(R"((\d{1,3}\.){3}\d{1,3}:\d{1,5})");
    QRegExpValidator* validator = new QRegExpValidator(ipPortRegExp);

    // 将验证器应用到输入框
    ui->qingbaoReceive->setValidator(validator);
    ui->targetAddress->setValidator(validator);

    // 连接采样率控件
    connect(ui->msgSampleSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            [this](int value) {
                QMetaObject::invokeMethod(worker, [this, value]() {
                    worker->setSamplingRate(value);
                }, Qt::QueuedConnection);
            });
    
    connect(ui->logSampleSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            [this](int value) {
                QMetaObject::invokeMethod(worker, [this, value]() {
                    worker->setLogSamplingRate(value);
                }, Qt::QueuedConnection);
            });

    // 连接菜单项
    connect(ui->actionExit, &QAction::triggered, this, &QApplication::quit);
    connect(ui->actionClearLogs, &QAction::triggered, this, [this]() {
        ui->qingbaoMsg->clear();
        logBuffer.clear();
        handleMessage(QString("[%1] <span class='system'>[系统] 日志已清除</span>")
                     .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
    });
    connect(ui->actionAbout, &QAction::triggered, this, [this]() {
        QMessageBox::about(this, "关于情报处理与数据转发系统", 
            "<h3>情报处理与数据转发系统 v1.0</h3>"
            "<p>实现UDP组播数据接收、文件读取和处理</p>"
            "<p>Copyright © 2023</p>");
    });

    // 初始化状态标签
    ui->lblThroughput->setText("处理速率: 0 消息/秒");
    ui->lblProcessTime->setText("平均处理时间: 0.00 毫秒");

    // 在启动时添加初始日志，确认日志系统正常工作
    handleMessage(QString("[%1] <span class='system'>[系统] 程序已启动，等待连接...</span>")
                 .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
}

QingBao::~QingBao()
{
    // 先停止工作线程中的任务
    if (worker) {
        QMetaObject::invokeMethod(worker, &QingBaoWorker::stop, Qt::QueuedConnection);
    }

    // 安全停止工作线程
    const int TIMEOUT_MS = 3000; // 3秒超时
    workerThread.quit();
    if (!workerThread.wait(TIMEOUT_MS)) {
        qWarning("QingBao worker thread failed to quit in %d ms, forcing termination", TIMEOUT_MS);
        workerThread.terminate();
        workerThread.wait();
    }

    // 停止定时器
    if (logUpdateTimer) {
        logUpdateTimer->stop();
        delete logUpdateTimer;
        logUpdateTimer = nullptr;
    }

    delete ui;
}

void QingBao::onConnect()
{
    if(connectState) {
        // 使用QMetaObject::invokeMethod确保在worker线程中执行方法
        QMetaObject::invokeMethod(worker, &QingBaoWorker::stop, Qt::QueuedConnection);

        ui->qingbao->setText("连接");
        handleMessage(QString("[%1] <span class='system'>[系统] 已断开连接</span>")
                     .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
    } else {
        // 确保工作线程已经启动
        if (!workerThread.isRunning()) {
            workerThread.start();
        }

        // 获取配置参数
        QString multicastAddress = ui->qingbaoReceive->text();
        QString targetAddress = ui->targetAddress->text();
        QString imageTargetAddress = ui->imageTargetAddress->text();
        QString ccdTargetAddress = ui->ccdTargetAddress->text();
        QString irTargetAddress = ui->irTargetAddress->text();


        // 配置并启动工作线程，使用QMetaObject::invokeMethod确保在worker线程中执行方法
        QMetaObject::invokeMethod(worker, [this, multicastAddress, targetAddress, 
                                          imageTargetAddress, ccdTargetAddress, irTargetAddress]() {
            worker->setMulticastAddress(multicastAddress);
            worker->setTargetAddress(targetAddress);
            worker->setImageTargetAddress(imageTargetAddress);
            worker->setCcdTargetAddress(ccdTargetAddress);
            worker->setIrTargetAddress(irTargetAddress);
            worker->start();
        }, Qt::QueuedConnection);

        ui->qingbao->setText("断开连接");
        handleMessage(QString("[%1] <span class='system'>[系统] 正在建立连接...</span>")
                     .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
    }
    connectState = !connectState;
}

void QingBao::handleMessage(const QString &message)
{
    QMutexLocker locker(&logMutex);
    if (logBuffer.size() >= MAX_LOG_BUFFER) {
        logBuffer.dequeue();
    }

    // 消息已经包含时间戳，由QingBaoWorker添加
    logBuffer.enqueue(message);
}

void QingBao::handleError(const QString &error)
{
    QMutexLocker locker(&logMutex);
    if (logBuffer.size() >= MAX_LOG_BUFFER) {
        logBuffer.dequeue();
    }

    // 错误消息已经包含时间戳，由QingBaoWorker添加
    logBuffer.enqueue("<span class='error'>" + error + "</span>");
}

void QingBao::updateLogDisplay()
{
    if(worker->logCounter > 500){
        worker->logCounter = 0;
        logBuffer.clear();
        ui->qingbaoMsg->clear();
    }
    QMutexLocker locker(&logMutex);
    if (logBuffer.isEmpty()) return;

    // 限制每次更新的消息数量，避免UI卡顿
    const int MAX_MESSAGES_PER_UPDATE = 50;
    int messagesToProcess = qMin(logBuffer.size(), MAX_MESSAGES_PER_UPDATE);
    
    QString allLogs;
    for (int i = 0; i < messagesToProcess; i++) {
        allLogs += logBuffer.dequeue() + "<br>";
    }

    // 获取当前滚动位置
    QScrollBar* scrollBar = ui->qingbaoMsg->verticalScrollBar();
    bool atBottom = scrollBar->value() >= scrollBar->maximum() - 10;

    // 添加新日志
    ui->qingbaoMsg->moveCursor(QTextCursor::End);
    ui->qingbaoMsg->insertHtml(allLogs);

    // 如果之前在底部，自动滚动到底部
    if (atBottom) {
        scrollBar->setValue(scrollBar->maximum());
    }
}

void QingBao::updateConnectionStatus(bool connected)
{
    QString style = connected ? "background-color: lime; border-radius: 8px;" : 
                               "background-color: red; border-radius: 8px;";
    ui->qingbaoLED->setStyleSheet(style);

    // 添加连接状态变更日志
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    if (connected) {
        handleMessage(QString("[%1] <span class='system'>[系统] 连接已建立</span>").arg(timestamp));
        
        // 更新连接按钮样式
        ui->qingbao->setStyleSheet("QPushButton { background-color: #e74c3c; color: white; border-radius: 4px; padding: 6px 12px; font-weight: bold; } "
                                 "QPushButton:hover { background-color: #c0392b; } "
                                 "QPushButton:pressed { background-color: #922b21; }");
    } else {
        handleMessage(QString("[%1] <span class='system'>[系统] 连接已断开</span>").arg(timestamp));
        
        // 恢复连接按钮样式
        ui->qingbao->setStyleSheet("");
    }
    
    // 更新状态栏
    statusBar()->showMessage(connected ? "已连接到UDP组播" : "未连接");
}

void QingBao::updateProcessingStats(int messagesPerSecond, double avgProcessTime)
{
    ui->lblThroughput->setText(QString("<b>处理速率:</b> %1 消息/秒")
                              .arg(messagesPerSecond));

    ui->lblProcessTime->setText(QString("<b>平均处理时间:</b> %1 毫秒")
                          .arg(avgProcessTime, 0, 'f', 2));
    
    // 更新状态栏
    statusBar()->showMessage(QString("处理: %1 消息/秒 | 处理时间: %2 毫秒")
                            .arg(messagesPerSecond)
                            .arg(avgProcessTime, 0, 'f', 2));
}
