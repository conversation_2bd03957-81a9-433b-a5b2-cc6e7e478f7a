﻿#include "Widget.h"
#include "ui_Widget.h"
#include <QTabWidget>
#include <QWidget>
#include <QVBoxLayout>
#include <QApplication>
#include <QStyle>
#include <QDesktopWidget>

Widget::Widget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::Widget),
    daotiao(nullptr),
    qing<PERSON>(nullptr),
    your<PERSON><PERSON>(nullptr)
{
    // 初始化UI
    ui->setupUi(this);

    // 创建标签页控件作为主界面容器
    QTabWidget *tabWidget = new QTabWidget(this);

    // 创建三个功能模块实例
    daotiao = new DaoTiao(this);    // 导调模块
    qingbao = new QingBao(this);    // 情报模块
    yourenji = new YouRenJi(this);  // 有人机模块

    // 将功能模块添加到标签页中
    tabWidget->addTab(daotiao, "导调");
    tabWidget->addTab(qingbao, "情报");
    tabWidget->addTab(your<PERSON>ji, "有人机");
    
    // 设置主窗口布局
    QVBoxLayout *layout = qobject_cast<QVBoxLayout*>(this->layout());
    if (!layout) {
        layout = new QVBoxLayout(this);
        this->setLayout(layout);
    }
    layout->addWidget(tabWidget);
    
    // 设置窗口标题和图标
    setWindowTitle("消息中转中心");
    setWindowIcon(QApplication::style()->standardIcon(QStyle::SP_ComputerIcon));
    
    // 将窗口居中显示
    this->setGeometry(
        QStyle::alignedRect(
            Qt::LeftToRight,
            Qt::AlignCenter,
            this->size(),
            QApplication::desktop()->availableGeometry()
        )
    );
}

Widget::~Widget()
{
    // 清理UI资源
    delete ui;
    // 注意：不需要手动删除子组件，因为它们是Widget的子对象，会自动删除
}
