#############################################################################
# Makefile for building: MsgCenter
# Generated by qmake (3.1) (Qt 5.13.0)
# Project:  MsgCenter.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_DEPRECATED_WARNINGS -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_WEBSOCKETS_LIB -DQT_NETWORK_LIB -DQT_CONCURRENT_LIB -DQT_SERIALPORT_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -O2 -Wall -W -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -W -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I. -I..\thirdpart\ffmpeg\include -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\include -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\include\QtWidgets -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\include\QtGui -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\include\QtANGLE -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\include\QtWebSockets -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\include\QtNetwork -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\include\QtConcurrent -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\include\QtSerialPort -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\include\QtCore -Irelease -I. -I\include -IC:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        -LD:\projs\MsgCenter\thirdpart\ffmpeg\lib -lavcodec -lavformat -lavutil -lswscale -LD:\projs\MsgCenter\thirdpart\ffmpeg\bin C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\libQt5Widgets.a C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\libQt5Gui.a C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\libQt5WebSockets.a C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\libQt5Network.a C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\libQt5Concurrent.a C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\libQt5SerialPort.a C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\libQt5Core.a  -lmingw32 C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\libqtmain.a -LC:\openssl\lib -LC:\Utils\postgresql\pgsql\lib -lshell32 
QMAKE         = C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\qmake.exe
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
COPY          = copy /y
SED           = $(QMAKE) -install sed
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
DEL_FILE      = del
DEL_DIR       = rmdir
MOVE          = move
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
INSTALL_FILE    = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR     = xcopy /s /q /y /i
QINSTALL        = C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\qmake.exe -install qinstall -exe

####### Output directory

OBJECTS_DIR   = release

####### Files

SOURCES       = DaoTiao.cpp \
		JustifyPosition.cpp \
		MessageWorker.cpp \
		QingBao.cpp \
		QingBaoWorker.cpp \
		YouRenJi.cpp \
		YouRenJiWorker.cpp \
		main.cpp \
		Widget.cpp release\moc_DaoTiao.cpp \
		release\moc_MessageWorker.cpp \
		release\moc_QingBao.cpp \
		release\moc_QingBaoWorker.cpp \
		release\moc_Widget.cpp \
		release\moc_YouRenJi.cpp \
		release\moc_YouRenJiWorker.cpp
OBJECTS       = release/DaoTiao.o \
		release/JustifyPosition.o \
		release/MessageWorker.o \
		release/QingBao.o \
		release/QingBaoWorker.o \
		release/YouRenJi.o \
		release/YouRenJiWorker.o \
		release/main.o \
		release/Widget.o \
		release/moc_DaoTiao.o \
		release/moc_MessageWorker.o \
		release/moc_QingBao.o \
		release/moc_QingBaoWorker.o \
		release/moc_Widget.o \
		release/moc_YouRenJi.o \
		release/moc_YouRenJiWorker.o

DIST          =  DaoTiao.h \
		JustifyPosition.h \
		MessageWorker.h \
		QingBao.h \
		QingBaoWorker.h \
		Widget.h \
		YouRenJi.h \
		YouRenJiWorker.h \
		protocol.h DaoTiao.cpp \
		JustifyPosition.cpp \
		MessageWorker.cpp \
		QingBao.cpp \
		QingBaoWorker.cpp \
		YouRenJi.cpp \
		YouRenJiWorker.cpp \
		main.cpp \
		Widget.cpp
QMAKE_TARGET  = MsgCenter
DESTDIR        = release\ #avoid trailing-slash linebreak
TARGET         = MsgCenter.exe
DESTDIR_TARGET = release\MsgCenter.exe

####### Build rules

first: all
all: Makefile.Release  release/MsgCenter.exe

release/MsgCenter.exe: C:/Qt/Qt5.13.0/5.13.0/mingw73_64/lib/libQt5Widgets.a C:/Qt/Qt5.13.0/5.13.0/mingw73_64/lib/libQt5Gui.a C:/Qt/Qt5.13.0/5.13.0/mingw73_64/lib/libQt5WebSockets.a C:/Qt/Qt5.13.0/5.13.0/mingw73_64/lib/libQt5Network.a C:/Qt/Qt5.13.0/5.13.0/mingw73_64/lib/libQt5Concurrent.a C:/Qt/Qt5.13.0/5.13.0/mingw73_64/lib/libQt5SerialPort.a C:/Qt/Qt5.13.0/5.13.0/mingw73_64/lib/libQt5Core.a C:/Qt/Qt5.13.0/5.13.0/mingw73_64/lib/libqtmain.a ui_DaoTiao.h ui_QingBao.h ui_Widget.h ui_YouRenJi.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @object_script.MsgCenter.Release  $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Release MsgCenter.pro

qmake_all: FORCE

dist:
	$(ZIP) MsgCenter.zip $(SOURCES) $(DIST) MsgCenter.pro C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\spec_pre.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\qdevice.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\device_config.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\common\sanitize.conf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\common\gcc-base.conf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\common\g++-base.conf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\common\angle.conf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\win32\windows_vulkan_sdk.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\common\windows-vulkan.conf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\common\g++-win32.conf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\common\windows-desktop.conf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\qconfig.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3danimation.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3danimation_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dcore.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dcore_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dextras.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dextras_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dinput.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dinput_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dlogic.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dlogic_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquick.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquick_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickanimation.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickextras.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickextras_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickinput.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickinput_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickrender.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickrender_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickscene2d.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3drender.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_3drender_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_accessibility_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_axbase.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_axbase_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_axcontainer.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_axcontainer_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_axserver.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_axserver_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_bluetooth.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_bluetooth_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_bodymovin_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_bootstrap_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_charts.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_charts_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_concurrent.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_concurrent_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_core.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_core_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_datavisualization.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_datavisualization_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_dbus.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_dbus_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_designer.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_designer_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_designercomponents_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_edid_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_egl_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_fb_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_fontdatabase_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_gamepad.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_gamepad_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_gui.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_gui_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_help.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_help_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_location.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_location_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_multimedia.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_multimedia_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_multimediawidgets.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_network.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_network_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_networkauth.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_networkauth_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_nfc.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_nfc_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_opengl.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_opengl_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_openglextensions.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_openglextensions_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_packetprotocol_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_platformcompositor_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_positioning.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_positioning_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_positioningquick.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_positioningquick_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_printsupport.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_printsupport_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_purchasing.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_purchasing_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_qml.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_qml_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_qmldebug_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_qmldevtools_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_qmltest.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_qmltest_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quick.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quick_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quickcontrols2.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quickparticles_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quickshapes_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quicktemplates2.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quickwidgets.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_quickwidgets_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_remoteobjects.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_remoteobjects_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_repparser.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_repparser_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_script.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_script_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_scripttools.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_scripttools_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_scxml.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_scxml_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_sensors.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_sensors_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_serialbus.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_serialbus_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_serialport.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_serialport_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_sql.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_sql_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_svg.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_svg_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_testlib.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_testlib_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_texttospeech.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_texttospeech_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_theme_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_uiplugin.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_uitools.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_uitools_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_virtualkeyboard.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_virtualkeyboard_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_vulkan_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_webchannel.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_webchannel_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_websockets.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_websockets_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_widgets.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_widgets_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_windowsuiautomation_support_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_winextras.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_winextras_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_xml.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_xml_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_xmlpatterns.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\modules\qt_lib_xmlpatterns_private.pri C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\qt_functions.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\qt_config.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\win32-g++\qmake.conf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\spec_post.prf .qmake.stash C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\exclusive_builds.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\toolchain.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\default_pre.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\win32\default_pre.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\resolve_config.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\exclusive_builds_post.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\default_post.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\build_pass.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\precompile_header.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\warn_on.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\qt.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\resources.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\moc.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\win32\opengl.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\uic.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\qmake_use.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\file_copies.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\win32\windows.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\testcase_targets.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\exceptions.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\yacc.prf C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\lex.prf MsgCenter.pro C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\Qt5Widgets.prl C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\Qt5Gui.prl C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\Qt5WebSockets.prl C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\Qt5Network.prl C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\Qt5Concurrent.prl C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\Qt5SerialPort.prl C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\Qt5Core.prl C:\Qt\Qt5.13.0\5.13.0\mingw73_64\lib\qtmain.prl    C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\data\dummy.cpp DaoTiao.h JustifyPosition.h MessageWorker.h QingBao.h QingBaoWorker.h Widget.h YouRenJi.h YouRenJiWorker.h protocol.h  DaoTiao.cpp JustifyPosition.cpp MessageWorker.cpp QingBao.cpp QingBaoWorker.cpp YouRenJi.cpp YouRenJiWorker.cpp main.cpp Widget.cpp DaoTiao.ui QingBao.ui Widget.ui YouRenJi.ui    

clean: compiler_clean 
	-$(DEL_FILE) release\DaoTiao.o release\JustifyPosition.o release\MessageWorker.o release\QingBao.o release\QingBaoWorker.o release\YouRenJi.o release\YouRenJiWorker.o release\main.o release\Widget.o release\moc_DaoTiao.o release\moc_MessageWorker.o release\moc_QingBao.o release\moc_QingBaoWorker.o release\moc_Widget.o release\moc_YouRenJi.o release\moc_YouRenJiWorker.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: release/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) release\moc_predefs.h
release/moc_predefs.h: C:/Qt/Qt5.13.0/5.13.0/mingw73_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -O2 -std=gnu++1z -Wall -W -Wextra -dM -E -o release\moc_predefs.h C:\Qt\Qt5.13.0\5.13.0\mingw73_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: release/moc_DaoTiao.cpp release/moc_MessageWorker.cpp release/moc_QingBao.cpp release/moc_QingBaoWorker.cpp release/moc_Widget.cpp release/moc_YouRenJi.cpp release/moc_YouRenJiWorker.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) release\moc_DaoTiao.cpp release\moc_MessageWorker.cpp release\moc_QingBao.cpp release\moc_QingBaoWorker.cpp release\moc_Widget.cpp release\moc_YouRenJi.cpp release\moc_YouRenJiWorker.cpp
release/moc_DaoTiao.cpp: DaoTiao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpalette.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qbrush.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfont.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qicon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdialog.h \
		MessageWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/QWebSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QUrl \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QAbstractSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkRequest \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QString \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QVariant \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkProxy \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkproxy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslError \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslerror.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslcertificate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcryptographichash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qssl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFlags \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslConfiguration \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslconfiguration.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsockets_global.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocketprotocol.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		release/moc_predefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/moc.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\moc.exe $(DEFINES) --include D:/projs/MsgCenter/MsgCenter/release/moc_predefs.h -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/mkspecs/win32-g++ -ID:/projs/MsgCenter/MsgCenter -ID:/projs/MsgCenter/thirdpart/ffmpeg/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtANGLE -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include -IC:/Strawberry/c/include -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed -IC:/Strawberry/c/i686-w64-mingw32/include -IC:/Strawberry/c/i686-w64-mingw32/include/c++ -IC:/Strawberry/c/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Strawberry/c/i686-w64-mingw32/include/c++/backward DaoTiao.h -o release\moc_DaoTiao.cpp

release/moc_MessageWorker.cpp: MessageWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/QWebSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QUrl \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QAbstractSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkRequest \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QString \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QVariant \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkProxy \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkproxy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslError \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslerror.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslcertificate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcryptographichash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qssl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFlags \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslConfiguration \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslconfiguration.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsockets_global.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocketprotocol.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		release/moc_predefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/moc.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\moc.exe $(DEFINES) --include D:/projs/MsgCenter/MsgCenter/release/moc_predefs.h -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/mkspecs/win32-g++ -ID:/projs/MsgCenter/MsgCenter -ID:/projs/MsgCenter/thirdpart/ffmpeg/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtANGLE -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include -IC:/Strawberry/c/include -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed -IC:/Strawberry/c/i686-w64-mingw32/include -IC:/Strawberry/c/i686-w64-mingw32/include/c++ -IC:/Strawberry/c/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Strawberry/c/i686-w64-mingw32/include/c++/backward MessageWorker.h -o release\moc_MessageWorker.cpp

release/moc_QingBao.cpp: QingBao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpalette.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qbrush.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfont.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qicon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QLabel \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdialog.h \
		release/moc_predefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/moc.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\moc.exe $(DEFINES) --include D:/projs/MsgCenter/MsgCenter/release/moc_predefs.h -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/mkspecs/win32-g++ -ID:/projs/MsgCenter/MsgCenter -ID:/projs/MsgCenter/thirdpart/ffmpeg/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtANGLE -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include -IC:/Strawberry/c/include -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed -IC:/Strawberry/c/i686-w64-mingw32/include -IC:/Strawberry/c/i686-w64-mingw32/include/c++ -IC:/Strawberry/c/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Strawberry/c/i686-w64-mingw32/include/c++/backward QingBao.h -o release\moc_QingBao.cpp

release/moc_QingBaoWorker.cpp: QingBaoWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QWaitCondition \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qwaitcondition.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonDocument \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsondocument.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonvalue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QAtomicInt \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSemaphore \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsemaphore.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThreadPool \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthreadpool.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrunnable.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDir \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdir.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfileinfo.h \
		JustifyPosition.h \
		protocol.h \
		../thirdpart/ffmpeg/include/libavcodec/avcodec.h \
		../thirdpart/ffmpeg/include/libavutil/samplefmt.h \
		../thirdpart/ffmpeg/include/libavutil/attributes.h \
		../thirdpart/ffmpeg/include/libavutil/avutil.h \
		../thirdpart/ffmpeg/include/libavutil/common.h \
		../thirdpart/ffmpeg/include/libavutil/error.h \
		../thirdpart/ffmpeg/include/libavutil/macros.h \
		../thirdpart/ffmpeg/include/libavutil/avconfig.h \
		../thirdpart/ffmpeg/include/libavcodec/version.h \
		../thirdpart/ffmpeg/include/libavutil/version.h \
		../thirdpart/ffmpeg/include/libavcodec/version_major.h \
		../thirdpart/ffmpeg/include/libavutil/mem.h \
		../thirdpart/ffmpeg/include/libavutil/rational.h \
		../thirdpart/ffmpeg/include/libavutil/mathematics.h \
		../thirdpart/ffmpeg/include/libavutil/intfloat.h \
		../thirdpart/ffmpeg/include/libavutil/log.h \
		../thirdpart/ffmpeg/include/libavutil/pixfmt.h \
		../thirdpart/ffmpeg/include/libavutil/buffer.h \
		../thirdpart/ffmpeg/include/libavutil/channel_layout.h \
		../thirdpart/ffmpeg/include/libavutil/dict.h \
		../thirdpart/ffmpeg/include/libavutil/frame.h \
		../thirdpart/ffmpeg/include/libavcodec/codec.h \
		../thirdpart/ffmpeg/include/libavutil/hwcontext.h \
		../thirdpart/ffmpeg/include/libavcodec/codec_id.h \
		../thirdpart/ffmpeg/include/libavcodec/defs.h \
		../thirdpart/ffmpeg/include/libavcodec/packet.h \
		../thirdpart/ffmpeg/include/libavcodec/codec_desc.h \
		../thirdpart/ffmpeg/include/libavcodec/codec_par.h \
		../thirdpart/ffmpeg/include/libavformat/avformat.h \
		../thirdpart/ffmpeg/include/libavformat/avio.h \
		../thirdpart/ffmpeg/include/libavformat/version_major.h \
		../thirdpart/ffmpeg/include/libavformat/version.h \
		../thirdpart/ffmpeg/include/libavutil/imgutils.h \
		../thirdpart/ffmpeg/include/libavutil/pixdesc.h \
		../thirdpart/ffmpeg/include/libavutil/opt.h \
		../thirdpart/ffmpeg/include/libswscale/swscale.h \
		release/moc_predefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/moc.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\moc.exe $(DEFINES) --include D:/projs/MsgCenter/MsgCenter/release/moc_predefs.h -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/mkspecs/win32-g++ -ID:/projs/MsgCenter/MsgCenter -ID:/projs/MsgCenter/thirdpart/ffmpeg/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtANGLE -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include -IC:/Strawberry/c/include -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed -IC:/Strawberry/c/i686-w64-mingw32/include -IC:/Strawberry/c/i686-w64-mingw32/include/c++ -IC:/Strawberry/c/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Strawberry/c/i686-w64-mingw32/include/c++/backward QingBaoWorker.h -o release\moc_QingBaoWorker.cpp

release/moc_Widget.cpp: Widget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QWidget \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpalette.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qbrush.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfont.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtouchdevice.h \
		DaoTiao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qicon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdialog.h \
		MessageWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/QWebSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QUrl \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QAbstractSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkRequest \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QString \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QVariant \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkProxy \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkproxy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslError \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslerror.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslcertificate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcryptographichash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qssl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFlags \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslConfiguration \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslconfiguration.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsockets_global.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocketprotocol.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		QingBao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QLabel \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		YouRenJi.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/QSerialPort \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialport.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialportglobal.h \
		YouRenJiWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QByteArray \
		release/moc_predefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/moc.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\moc.exe $(DEFINES) --include D:/projs/MsgCenter/MsgCenter/release/moc_predefs.h -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/mkspecs/win32-g++ -ID:/projs/MsgCenter/MsgCenter -ID:/projs/MsgCenter/thirdpart/ffmpeg/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtANGLE -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include -IC:/Strawberry/c/include -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed -IC:/Strawberry/c/i686-w64-mingw32/include -IC:/Strawberry/c/i686-w64-mingw32/include/c++ -IC:/Strawberry/c/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Strawberry/c/i686-w64-mingw32/include/c++/backward Widget.h -o release\moc_Widget.cpp

release/moc_YouRenJi.cpp: YouRenJi.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpalette.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qbrush.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfont.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qicon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/QSerialPort \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialport.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialportglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdialog.h \
		YouRenJiWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QByteArray \
		release/moc_predefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/moc.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\moc.exe $(DEFINES) --include D:/projs/MsgCenter/MsgCenter/release/moc_predefs.h -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/mkspecs/win32-g++ -ID:/projs/MsgCenter/MsgCenter -ID:/projs/MsgCenter/thirdpart/ffmpeg/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtANGLE -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include -IC:/Strawberry/c/include -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed -IC:/Strawberry/c/i686-w64-mingw32/include -IC:/Strawberry/c/i686-w64-mingw32/include/c++ -IC:/Strawberry/c/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Strawberry/c/i686-w64-mingw32/include/c++/backward YouRenJi.h -o release\moc_YouRenJi.cpp

release/moc_YouRenJiWorker.cpp: YouRenJiWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/QSerialPort \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialport.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialportglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QByteArray \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		release/moc_predefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/moc.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\moc.exe $(DEFINES) --include D:/projs/MsgCenter/MsgCenter/release/moc_predefs.h -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/mkspecs/win32-g++ -ID:/projs/MsgCenter/MsgCenter -ID:/projs/MsgCenter/thirdpart/ffmpeg/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtANGLE -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort -IC:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include -IC:/Strawberry/c/include -IC:/Strawberry/c/lib/gcc/i686-w64-mingw32/4.9.2/include-fixed -IC:/Strawberry/c/i686-w64-mingw32/include -IC:/Strawberry/c/i686-w64-mingw32/include/c++ -IC:/Strawberry/c/i686-w64-mingw32/include/c++/i686-w64-mingw32 -IC:/Strawberry/c/i686-w64-mingw32/include/c++/backward YouRenJiWorker.h -o release\moc_YouRenJiWorker.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_DaoTiao.h ui_QingBao.h ui_Widget.h ui_YouRenJi.h
compiler_uic_clean:
	-$(DEL_FILE) ui_DaoTiao.h ui_QingBao.h ui_Widget.h ui_YouRenJi.h
ui_DaoTiao.h: DaoTiao.ui \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/uic.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\uic.exe DaoTiao.ui -o ui_DaoTiao.h

ui_QingBao.h: QingBao.ui \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/uic.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\uic.exe QingBao.ui -o ui_QingBao.h

ui_Widget.h: Widget.ui \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/uic.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\uic.exe Widget.ui -o ui_Widget.h

ui_YouRenJi.h: YouRenJi.ui \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/bin/uic.exe
	C:\Qt\Qt5.13.0\5.13.0\mingw73_64\bin\uic.exe YouRenJi.ui -o ui_YouRenJi.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

release/DaoTiao.o: DaoTiao.cpp DaoTiao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpalette.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qbrush.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfont.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qicon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdialog.h \
		MessageWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/QWebSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QUrl \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QAbstractSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkRequest \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QString \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QVariant \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkProxy \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkproxy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslError \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslerror.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslcertificate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcryptographichash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qssl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFlags \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslConfiguration \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslconfiguration.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsockets_global.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocketprotocol.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		ui_DaoTiao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDebug \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QRegExp \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QRegExpValidator \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvalidator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregularexpression.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QScrollBar \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qscrollbar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qabstractslider.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QSpinBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qspinbox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QLabel \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QVBoxLayout \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qboxlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlayoutitem.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qgridlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QTextCursor \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtextcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtextformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpen.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtextoption.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QFontDatabase \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontdatabase.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMetaType \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QApplication \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qinputmethod.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QStyle \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qstyle.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\DaoTiao.o DaoTiao.cpp

release/JustifyPosition.o: JustifyPosition.cpp JustifyPosition.h \
		protocol.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\JustifyPosition.o JustifyPosition.cpp

release/MessageWorker.o: MessageWorker.cpp MessageWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/QWebSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QUrl \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QAbstractSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkRequest \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QString \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QVariant \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkProxy \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkproxy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslError \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslerror.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslcertificate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcryptographichash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qssl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFlags \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslConfiguration \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslconfiguration.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsockets_global.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocketprotocol.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonDocument \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsondocument.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonvalue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonParseError \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonArray \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonarray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDebug \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QCoreApplication \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMetaType \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QtMath \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDir \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdir.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfileinfo.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\MessageWorker.o MessageWorker.cpp

release/QingBao.o: QingBao.cpp QingBao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpalette.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qbrush.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfont.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qicon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QLabel \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdialog.h \
		ui_QingBao.h \
		QingBaoWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QWaitCondition \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qwaitcondition.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonDocument \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsondocument.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonvalue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QAtomicInt \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSemaphore \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsemaphore.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThreadPool \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthreadpool.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrunnable.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDir \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdir.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfileinfo.h \
		JustifyPosition.h \
		protocol.h \
		../thirdpart/ffmpeg/include/libavcodec/avcodec.h \
		../thirdpart/ffmpeg/include/libavutil/samplefmt.h \
		../thirdpart/ffmpeg/include/libavutil/attributes.h \
		../thirdpart/ffmpeg/include/libavutil/avutil.h \
		../thirdpart/ffmpeg/include/libavutil/common.h \
		../thirdpart/ffmpeg/include/libavutil/error.h \
		../thirdpart/ffmpeg/include/libavutil/macros.h \
		../thirdpart/ffmpeg/include/libavutil/avconfig.h \
		../thirdpart/ffmpeg/include/libavcodec/version.h \
		../thirdpart/ffmpeg/include/libavutil/version.h \
		../thirdpart/ffmpeg/include/libavcodec/version_major.h \
		../thirdpart/ffmpeg/include/libavutil/mem.h \
		../thirdpart/ffmpeg/include/libavutil/rational.h \
		../thirdpart/ffmpeg/include/libavutil/mathematics.h \
		../thirdpart/ffmpeg/include/libavutil/intfloat.h \
		../thirdpart/ffmpeg/include/libavutil/log.h \
		../thirdpart/ffmpeg/include/libavutil/pixfmt.h \
		../thirdpart/ffmpeg/include/libavutil/buffer.h \
		../thirdpart/ffmpeg/include/libavutil/channel_layout.h \
		../thirdpart/ffmpeg/include/libavutil/dict.h \
		../thirdpart/ffmpeg/include/libavutil/frame.h \
		../thirdpart/ffmpeg/include/libavcodec/codec.h \
		../thirdpart/ffmpeg/include/libavutil/hwcontext.h \
		../thirdpart/ffmpeg/include/libavcodec/codec_id.h \
		../thirdpart/ffmpeg/include/libavcodec/defs.h \
		../thirdpart/ffmpeg/include/libavcodec/packet.h \
		../thirdpart/ffmpeg/include/libavcodec/codec_desc.h \
		../thirdpart/ffmpeg/include/libavcodec/codec_par.h \
		../thirdpart/ffmpeg/include/libavformat/avformat.h \
		../thirdpart/ffmpeg/include/libavformat/avio.h \
		../thirdpart/ffmpeg/include/libavformat/version_major.h \
		../thirdpart/ffmpeg/include/libavformat/version.h \
		../thirdpart/ffmpeg/include/libavutil/imgutils.h \
		../thirdpart/ffmpeg/include/libavutil/pixdesc.h \
		../thirdpart/ffmpeg/include/libavutil/opt.h \
		../thirdpart/ffmpeg/include/libswscale/swscale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDebug \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QRegExp \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QRegExpValidator \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvalidator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregularexpression.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QScrollBar \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qscrollbar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qabstractslider.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QSpinBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qspinbox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QVBoxLayout \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qboxlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlayoutitem.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qgridlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QTextCursor \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtextcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtextformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpen.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtextoption.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QFontDatabase \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontdatabase.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMetaType \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QFileDialog \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qfiledialog.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QGroupBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qgroupbox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QStyleFactory \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qstylefactory.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QApplication \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qinputmethod.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QPalette \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QStyle \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qstyle.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\QingBao.o QingBao.cpp

release/QingBaoWorker.o: QingBaoWorker.cpp QingBaoWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QWaitCondition \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qwaitcondition.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonDocument \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsondocument.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonvalue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QAtomicInt \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSemaphore \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsemaphore.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThreadPool \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthreadpool.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrunnable.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDir \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdir.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfileinfo.h \
		JustifyPosition.h \
		protocol.h \
		../thirdpart/ffmpeg/include/libavcodec/avcodec.h \
		../thirdpart/ffmpeg/include/libavutil/samplefmt.h \
		../thirdpart/ffmpeg/include/libavutil/attributes.h \
		../thirdpart/ffmpeg/include/libavutil/avutil.h \
		../thirdpart/ffmpeg/include/libavutil/common.h \
		../thirdpart/ffmpeg/include/libavutil/error.h \
		../thirdpart/ffmpeg/include/libavutil/macros.h \
		../thirdpart/ffmpeg/include/libavutil/avconfig.h \
		../thirdpart/ffmpeg/include/libavcodec/version.h \
		../thirdpart/ffmpeg/include/libavutil/version.h \
		../thirdpart/ffmpeg/include/libavcodec/version_major.h \
		../thirdpart/ffmpeg/include/libavutil/mem.h \
		../thirdpart/ffmpeg/include/libavutil/rational.h \
		../thirdpart/ffmpeg/include/libavutil/mathematics.h \
		../thirdpart/ffmpeg/include/libavutil/intfloat.h \
		../thirdpart/ffmpeg/include/libavutil/log.h \
		../thirdpart/ffmpeg/include/libavutil/pixfmt.h \
		../thirdpart/ffmpeg/include/libavutil/buffer.h \
		../thirdpart/ffmpeg/include/libavutil/channel_layout.h \
		../thirdpart/ffmpeg/include/libavutil/dict.h \
		../thirdpart/ffmpeg/include/libavutil/frame.h \
		../thirdpart/ffmpeg/include/libavcodec/codec.h \
		../thirdpart/ffmpeg/include/libavutil/hwcontext.h \
		../thirdpart/ffmpeg/include/libavcodec/codec_id.h \
		../thirdpart/ffmpeg/include/libavcodec/defs.h \
		../thirdpart/ffmpeg/include/libavcodec/packet.h \
		../thirdpart/ffmpeg/include/libavcodec/codec_desc.h \
		../thirdpart/ffmpeg/include/libavcodec/codec_par.h \
		../thirdpart/ffmpeg/include/libavformat/avformat.h \
		../thirdpart/ffmpeg/include/libavformat/avio.h \
		../thirdpart/ffmpeg/include/libavformat/version_major.h \
		../thirdpart/ffmpeg/include/libavformat/version.h \
		../thirdpart/ffmpeg/include/libavutil/imgutils.h \
		../thirdpart/ffmpeg/include/libavutil/pixdesc.h \
		../thirdpart/ffmpeg/include/libavutil/opt.h \
		../thirdpart/ffmpeg/include/libswscale/swscale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QHostAddress \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkDatagram \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkdatagram.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QImage \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QBuffer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbuffer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QRunnable \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QCoreApplication \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QProcess \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocess.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QStandardPaths \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstandardpaths.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTemporaryDir \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtemporarydir.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\QingBaoWorker.o QingBaoWorker.cpp

release/YouRenJi.o: YouRenJi.cpp YouRenJi.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpalette.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qbrush.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfont.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtouchdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qicon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/QSerialPort \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialport.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialportglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdialog.h \
		YouRenJiWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QByteArray \
		ui_YouRenJi.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDebug \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QRegExp \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QRegExpValidator \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvalidator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregularexpression.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QScrollBar \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qscrollbar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qabstractslider.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QSpinBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qspinbox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QLabel \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QVBoxLayout \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qboxlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlayoutitem.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qgridlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QTextCursor \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtextcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtextformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpen.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtextoption.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/QFontDatabase \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontdatabase.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMetaType \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/QSerialPortInfo \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialportinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QApplication \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qinputmethod.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QStyle \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qstyle.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\YouRenJi.o YouRenJi.cpp

release/YouRenJiWorker.o: YouRenJiWorker.cpp YouRenJiWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/QSerialPort \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialport.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialportglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QByteArray \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonDocument \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsondocument.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonvalue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QJsonParseError \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDebug \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QCoreApplication \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/QtConcurrent \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/QtConcurrentDepends \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QtCore \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QtCoreDepends \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qabstractanimation.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qabstracteventdispatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qabstractitemmodel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qabstractnativeeventfilter.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qabstractproxymodel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qabstractstate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qabstracttransition.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qanimationgroup.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydataops.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydatapointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbitarray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbuffer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraymatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcborarray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcborvalue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcborcommon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregularexpression.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/quuid.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcbormap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcborstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfloat16.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcollator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcommandlineoption.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcommandlineparser.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconcatenatetablesproxymodel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcryptographichash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdeadlinetimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdir.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfileinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdiriterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeasingcurve.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qendian.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeventtransition.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qexception.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfactoryinterface.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfileselector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QStringList \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfilesystemwatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfinalstate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfuture.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfutureinterface.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrunnable.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qresultstore.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfuturesynchronizer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfuturewatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhistorystate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qidentityproxymodel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qisenum.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qitemselectionmodel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qjsonarray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlibrary.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlibraryinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversionnumber.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlinkedlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlockfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qloggingcategory.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmessageauthenticationcode.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetaobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmimedata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmimedatabase.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmimetype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectcleanuphandler.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qoperatingsystemversion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qparallelanimationgroup.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpauseanimation.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qplugin.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpluginloader.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocess.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpropertyanimation.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariantanimation.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrandom.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qreadwritelock.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qresource.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsavefile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedvaluerollback.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopeguard.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsemaphore.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsequentialanimationgroup.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsettings.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedmemory.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsignalmapper.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsignaltransition.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsocketnotifier.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsortfilterproxymodel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstack.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstandardpaths.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstatemachine.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstorageinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlistmodel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemsemaphore.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtemporarydir.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtemporaryfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextboundaryfinder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextcodec.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthreadpool.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthreadstorage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimeline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimezone.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtranslator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtransposeproxymodel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypetraits.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qwaitcondition.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qwineventnotifier.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qxmlstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcoreversion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentcompilertest.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrent_global.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentexception.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentfilter.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentfilterkernel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentiteratekernel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentmedian.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentthreadengine.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentmapkernel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentreducekernel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentfunctionwrappers.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentrun.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentrunbase.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentstoredfunctioncall.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtConcurrent/qtconcurrentversion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFileInfo \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDir
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\YouRenJiWorker.o YouRenJiWorker.cpp

release/main.o: main.cpp Widget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QWidget \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpalette.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qbrush.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfont.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtouchdevice.h \
		DaoTiao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qicon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdialog.h \
		MessageWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/QWebSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QUrl \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QAbstractSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkRequest \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QString \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QVariant \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkProxy \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkproxy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslError \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslerror.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslcertificate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcryptographichash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qssl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFlags \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslConfiguration \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslconfiguration.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsockets_global.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocketprotocol.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		QingBao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QLabel \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		YouRenJi.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/QSerialPort \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialport.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialportglobal.h \
		YouRenJiWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QByteArray \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QApplication \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qinputmethod.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\main.o main.cpp

release/Widget.o: Widget.cpp Widget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QWidget \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtguiglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig-bootstrapped.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qconfig.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtcore-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsystemdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qprocessordetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtypeinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsysinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlogging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qflags.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasicatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_bootstrap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qgenericatomic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qatomic_msvc.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qglobalstatic.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmutex.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnumeric.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qversiontagging.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtgui-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qnamespace.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstring.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qchar.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrefcount.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qarraydata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringliteral.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringview.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringbuilder.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qalgorithms.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiterator.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhashfunctions.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpair.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvector.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qpoint.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbytearraylist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringlist.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qregexp.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qstringmatcher.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qscopedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmetatype.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qobject_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmargins.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpaintdevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qrect.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsize.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpalette.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcolor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgb.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qrgba64.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qbrush.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qmatrix.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpolygon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qregion.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatastream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qiodevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qline.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtransform.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpainterpath.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qimage.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixelformat.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qpixmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qshareddata.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qhash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfont.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontmetrics.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qfontinfo.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qcursor.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qkeysequence.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qevent.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qvariant.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qmap.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdebug.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtextstream.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qlocale.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qset.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qurlquery.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfile.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qfiledevice.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qvector2d.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qtouchdevice.h \
		DaoTiao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMainWindow \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qicon.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qbasictimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QThread \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qthread.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QQueue \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qqueue.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QMutex \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QMessageBox \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdialog.h \
		MessageWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QObject \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/QWebSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QUrl \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QAbstractSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkRequest \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QSharedDataPointer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QString \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QVariant \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QNetworkProxy \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qnetworkproxy.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslError \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslerror.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslcertificate.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcryptographichash.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qdatetime.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qssl.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFlags \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QSslConfiguration \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslconfiguration.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qsslsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qtcpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsockets_global.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWebSockets/qwebsocketprotocol.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/QUdpSocket \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QElapsedTimer \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QHash \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QFile \
		QingBao.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QLabel \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlabel.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qframe.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QDateTime \
		YouRenJi.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/QSerialPort \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialport.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtSerialPort/qserialportglobal.h \
		YouRenJiWorker.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/QByteArray \
		ui_Widget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QTabWidget \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QVBoxLayout \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qboxlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qlayoutitem.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qgridlayout.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QApplication \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qcoreapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtCore/qeventloop.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qdesktopwidget.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qguiapplication.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtGui/qinputmethod.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QStyle \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/qstyle.h \
		C:/Qt/Qt5.13.0/5.13.0/mingw73_64/include/QtWidgets/QDesktopWidget
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\Widget.o Widget.cpp

release/moc_DaoTiao.o: release/moc_DaoTiao.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_DaoTiao.o release\moc_DaoTiao.cpp

release/moc_MessageWorker.o: release/moc_MessageWorker.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_MessageWorker.o release\moc_MessageWorker.cpp

release/moc_QingBao.o: release/moc_QingBao.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_QingBao.o release\moc_QingBao.cpp

release/moc_QingBaoWorker.o: release/moc_QingBaoWorker.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_QingBaoWorker.o release\moc_QingBaoWorker.cpp

release/moc_Widget.o: release/moc_Widget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_Widget.o release\moc_Widget.cpp

release/moc_YouRenJi.o: release/moc_YouRenJi.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_YouRenJi.o release\moc_YouRenJi.cpp

release/moc_YouRenJiWorker.o: release/moc_YouRenJiWorker.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_YouRenJiWorker.o release\moc_YouRenJiWorker.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

