﻿#ifndef WIDGET_H
#define WIDGET_H

#include <QWidget>
#include "DaoTiao.h"
#include "QingBao.h"
#include "YouRenJi.h"

namespace Ui {
class Widget;
}

/**
 * @brief 主窗口类
 * 
 * 该类是应用程序的主窗口，负责管理和组织三个主要功能模块：
 * - 导调模块（DaoTiao）
 * - 情报模块（QingBao）
 * - 有人机模块（YouRenJi）
 */
class Widget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit Widget(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * 负责清理资源，包括UI和功能模块实例
     */
    ~Widget();

private:
    Ui::Widget *ui;          ///< UI对象指针
    DaoTiao *daotiao;        ///< 导调模块实例
    QingBao *qingbao;        ///< 情报模块实例
    YouRenJi *yourenji;      ///< 有人机模块实例
};

#endif // WIDGET_H
