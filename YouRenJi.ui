<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>YouRenJi</class>
 <widget class="QMainWindow" name="YouRenJ<PERSON>">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>650</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>串口到载荷组播转发器</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QMainWindow {
    background-color: #f5f5f5;
}
QGroupBox {
    border: 1px solid #cccccc;
    border-radius: 5px;
    margin-top: 1ex;
    font-weight: bold;
    background-color: #ffffff;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 3px 0 3px;
    color: #2c3e50;
}
QPushButton {
    background-color: #3498db;
    color: white;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #2980b9;
}
QPushButton:pressed {
    background-color: #1a5276;
}
QLineEdit {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 4px;
    background-color: #f8f9fa;
}
QLineEdit:focus {
    border: 1px solid #3498db;
}
QTextBrowser {
    background-color: #ffffff;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-family: Consolas, Monaco, Monospace;
}
QLabel {
    color: #2c3e50;
}
QComboBox {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 4px;
    background-color: #f8f9fa;
}
QComboBox:focus {
    border: 1px solid #3498db;
}
QComboBox::drop-down {
    border-left: 1px solid #bdc3c7;
    width: 20px;
}
QSpinBox {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 2px;
}</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <property name="leftMargin">
     <number>10</number>
    </property>
    <property name="topMargin">
     <number>10</number>
    </property>
    <property name="rightMargin">
     <number>10</number>
    </property>
    <property name="bottomMargin">
     <number>10</number>
    </property>
    <property name="spacing">
     <number>10</number>
    </property>
    <item row="0" column="0">
     <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,1">
      <property name="spacing">
       <number>10</number>
      </property>
      <item>
       <widget class="QGroupBox" name="groupBox_2">
        <property name="title">
         <string>日志面板</string>
        </property>
        <layout class="QGridLayout" name="gridLayout_3">
         <property name="leftMargin">
          <number>8</number>
         </property>
         <property name="topMargin">
          <number>20</number>
         </property>
         <property name="rightMargin">
          <number>8</number>
         </property>
         <property name="bottomMargin">
          <number>8</number>
         </property>
         <item row="0" column="0">
          <widget class="QTextBrowser" name="yourenjiMsg">
           <property name="styleSheet">
            <string notr="true">QTextBrowser {
    font-size: 9pt;
    line-height: 1.4;
}</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox">
        <property name="title">
         <string>配置面板</string>
        </property>
        <layout class="QGridLayout" name="gridLayout_4">
         <property name="leftMargin">
          <number>8</number>
         </property>
         <property name="topMargin">
          <number>20</number>
         </property>
         <property name="rightMargin">
          <number>8</number>
         </property>
         <property name="bottomMargin">
          <number>8</number>
         </property>
         <item row="0" column="0">
          <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0,0,0,0,0">
           <property name="spacing">
            <number>12</number>
           </property>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <widget class="QLabel" name="label_6">
               <property name="styleSheet">
                <string notr="true">font-weight: bold;
font-size: 10pt;</string>
               </property>
               <property name="text">
                <string>连接状态：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="led">
               <property name="minimumSize">
                <size>
                 <width>16</width>
                 <height>16</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16</width>
                 <height>16</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">background-color: red;
border-radius: 8px;</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QGroupBox" name="networkGroup">
             <property name="title">
              <string>网络参数</string>
             </property>
             <layout class="QFormLayout" name="networkForm">
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>15</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>6</number>
              </property>
              <property name="horizontalSpacing">
               <number>8</number>
              </property>
              <property name="verticalSpacing">
               <number>8</number>
              </property>
              <item row="0" column="0">
               <widget class="QLabel" name="label_3">
                <property name="text">
                 <string>接收方(无人机)</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QLineEdit" name="uavReceive">
                <property name="placeholderText">
                 <string>IP地址:端口</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="label_4">
                <property name="text">
                 <string>发送至(载荷)</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QLineEdit" name="zaiheSend">
                <property name="placeholderText">
                 <string>IP地址:端口</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_3">
             <property name="title">
              <string>串口配置</string>
             </property>
             <layout class="QGridLayout" name="gridLayout_6">
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>15</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>6</number>
              </property>
              <property name="spacing">
               <number>8</number>
              </property>
              <item row="0" column="0">
               <widget class="QLabel" name="label_7">
                <property name="text">
                 <string>COM 端口</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QComboBox" name="ComPort"/>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="label_8">
                <property name="text">
                 <string>波特率</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QComboBox" name="BaudRate"/>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="label_9">
                <property name="text">
                 <string>校验位</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QComboBox" name="ParityBits"/>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="label_10">
                <property name="text">
                 <string>数据位</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QComboBox" name="DataBits"/>
              </item>
              <item row="4" column="0">
               <widget class="QLabel" name="label_11">
                <property name="text">
                 <string>停止位</string>
                </property>
               </widget>
              </item>
              <item row="4" column="1">
               <widget class="QComboBox" name="StopBits"/>
              </item>
              <item row="5" column="0">
               <widget class="QLabel" name="COMState">
                <property name="font">
                 <font>
                  <weight>75</weight>
                  <bold>true</bold>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color: red;</string>
                </property>
                <property name="text">
                 <string>已关闭</string>
                </property>
               </widget>
              </item>
              <item row="5" column="1">
               <widget class="QPushButton" name="COMClick">
                <property name="text">
                 <string>打开串口</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_4">
             <property name="title">
              <string>采样设置</string>
             </property>
             <layout class="QFormLayout" name="formLayout">
              <property name="leftMargin">
               <number>6</number>
              </property>
              <property name="topMargin">
               <number>15</number>
              </property>
              <property name="rightMargin">
               <number>6</number>
              </property>
              <property name="bottomMargin">
               <number>6</number>
              </property>
              <property name="horizontalSpacing">
               <number>8</number>
              </property>
              <property name="verticalSpacing">
               <number>8</number>
              </property>
              <item row="0" column="0">
               <widget class="QLabel" name="msgRateLabel">
                <property name="text">
                 <string>消息采样率:</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QSpinBox" name="msgSampleSpinBox">
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <number>100</number>
                </property>
                <property name="value">
                 <number>1</number>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="logRateLabel">
                <property name="text">
                 <string>日志采样率:</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QSpinBox" name="logSampleSpinBox">
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="maximum">
                 <number>100</number>
                </property>
                <property name="value">
                 <number>10</number>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="2">
               <widget class="QLabel" name="throughputLabel">
                <property name="text">
                 <string>消息吞吐量: 0 消息/秒</string>
                </property>
               </widget>
              </item>
              <item row="3" column="0" colspan="2">
               <widget class="QLabel" name="bytesLabel">
                <property name="text">
                 <string>数据处理量: 0 字节/秒</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="yourenji">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>35</height>
              </size>
             </property>
             <property name="text">
              <string>启动转发</string>
             </property>
             <property name="icon">
              <iconset theme="network-wired"/>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>900</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>文件</string>
    </property>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuTools">
    <property name="title">
     <string>工具</string>
    </property>
    <addaction name="actionClearLogs"/>
    <addaction name="actionRefreshPorts"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>帮助</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuTools"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionExit">
   <property name="text">
    <string>退出</string>
   </property>
  </action>
  <action name="actionClearLogs">
   <property name="text">
    <string>清除日志</string>
   </property>
  </action>
  <action name="actionRefreshPorts">
   <property name="text">
    <string>刷新串口列表</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>关于</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
