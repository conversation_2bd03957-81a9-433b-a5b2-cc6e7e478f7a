﻿#ifndef DAOTIAO_H
#define DAOTIAO_H

#include <QMainWindow>
#include <QTimer>
#include <QThread>
#include <QQueue>
#include <QMutex>
#include <QMessageBox>
#include "MessageWorker.h"

namespace Ui {
class DaoTiao;
}

/**
 * @brief 导调模块主窗口类
 * 
 * 该类负责导调功能的实现，包括：
 * - WebSocket通信管理
 * - UDP组播数据处理
 * - 性能监控和统计
 * - 日志记录和显示
 */
class DaoTiao : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit DaoTiao(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * 负责清理资源，包括工作线程、定时器等
     */
    ~DaoTiao();

private slots:
    /**
     * @brief 连接/断开连接按钮点击处理
     */
    void onConnect();

    /**
     * @brief WebSocket连接成功处理
     */
    void handleWebSocketConnected();

    /**
     * @brief WebSocket断开连接处理
     */
    void handleWebSocketDisconnected();

    /**
     * @brief 处理一般消息
     * @param message 待处理的消息
     */
    void handleMessage(const QString &message);

    /**
     * @brief 处理错误消息
     * @param error 错误信息
     */
    void handleError(const QString &error);

    /**
     * @brief 更新连接状态
     * @param connected 是否已连接
     */
    void updateConnectionStatus(bool connected);

    /**
     * @brief 更新性能统计信息
     * @param messagesPerSecond 每秒消息数
     * @param packetsPerSecond 每秒数据包数
     * @param avgLatency 平均延迟（毫秒）
     */
    void updateThroughput(int messagesPerSecond, int packetsPerSecond, double avgLatency);

    /**
     * @brief 更新日志显示
     * 将缓冲区中的日志消息显示到界面上
     */
    void updateLogDisplay();

    /**
     * @brief 清除日志
     * 清空日志缓冲区和显示区域
     */
    void clearLogs();

private:
    Ui::DaoTiao *ui;                 ///< UI对象指针
    QTimer *reconnectTimer;          ///< 重连定时器
    bool connectState = false;        ///< 连接状态
    bool dtWebRecvConnected = false; ///< 导调接收WebSocket连接状态
    bool dtWebSndConnected = false;  ///< 导调发送WebSocket连接状态

    QThread workerThread;            ///< 工作线程
    MessageWorker *worker;           ///< 消息处理工作对象
    QTimer *logUpdateTimer;          ///< 日志更新定时器
    QQueue<QString> logBuffer;       ///< 日志缓冲区
    QMutex logMutex;                ///< 日志互斥锁

    static const int MAX_LOG_BUFFER = 500;        ///< 最大日志缓冲区大小
    static const int LOG_UPDATE_INTERVAL = 200;    ///< 日志更新间隔（毫秒）
};

#endif // DAOTIAO_H
