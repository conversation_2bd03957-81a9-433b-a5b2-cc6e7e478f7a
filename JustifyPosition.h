﻿#ifndef JUSTIFYPOSITION_H
#define JUSTIFYPOSITION_H
#include "protocol.h"

class JustifyPosition
{
public:
    struct JW BLToXY(double latitude, double longitude);
    struct JW XYToBL(double X, double Y);

    double GetDoubleMaxValue(double* array, int count);

    double GetDoubleMinValue(double* array, int count);
    /******************************InitParamBX**********************************/
    int InitParamBX(int inImageType, double fLongitude, double fLatitude, double fFlyHeight, double fTerrainHeight, double fPlanePitch, double fPlaneRoll, double fPlaneYaw, double fCameraPitch, double fCameraYaw, double fCameraFocus, double fCameraPixelSize, int imgW, int imgH);
    /******************************GeoCorrectBX**********************************/
    struct JWInfo GeoCorrectBX(CVPOINT2D32F(*srcpoint_out)[4], CVPOINT2D32F(*dstpoint_out)[4]);


    /**********************************Main**************************************/
    //const INPUT_TESTParam &INPUT_TEST,OutputParam *const output
    int getImageLocationInfo(const struct INPUT_TEST INPUT_TEST, struct CORRECTION_PARAM* const output);


private:
    /*******************************Constants defined************************************/
    const double pi = 3.14159265358979;
    /******************************Variable definitions**********************************/
    double m_fLongitude;//飞机经度
    double m_fLatitude;//飞机经度
    double m_fFlyHeight;//飞机高度
    double m_fTerrainHeight;//地形高度
    double m_fPlanePitch;
    double m_fPlaneRoll;
    double m_fPlaneYaw;
    double m_fCameraPitch;
    double m_fCameraYaw;
    double m_fCameraFocus;
    double m_fCameraPixelSize;
    double m_imgW;
    double m_imgH;
};
#endif // JUSTIFYPOSITION_H
