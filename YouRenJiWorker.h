#ifndef YOURENJIWORKER_H
#define YOURENJIWORKER_H

#include <QObject>
#include <QUdpSocket>
#include <QSerialPort>
#include <QTimer>
#include <QQueue>
#include <QMutex>
#include <QElapsedTimer>
#include <QHash>
#include <QByteArray>
#include <QThread>

#ifdef SERIALPORT
#define SERIALPORT

#endif

class Helper : public QObject {
    Q_OBJECT
public:
    explicit Helper(QObject *parent = nullptr) : QObject(parent){}

signals:
    void execute();
public slots:
    void trigger(){
        emit execute();
    }
};

enum CmdTotal
{
    CONTROL=1021,       //  21  手控1
    SELFINSPECTION,     //  22  自检1
    RETURNZERO,         //  23  归零1
    TAKEPHOTO,          //  24  拍照1
    RETURNVIEWSTATE,    //  25  相机垂直下视1
    VIEWUP,             //  26  载荷向上1
    VIEWDOWN,           //  27  载荷向下1
    VIEWLEFT,           //  28  载荷向左1
    VIEWRIGHT,          //  29  载荷向右1
    TARGETTRACK,        //  30  目标跟踪1
    POSITIONTRACK,      //  31  地理跟踪1
    SWITCHLOAD,         //  32  跟踪监视主通道切换1
    CCDCHANGEZOOM,      //  33  电视变焦1
    IRCHANGEZOOM,       //  34  红外变焦1
    CHANGESENSITIVITY,  //  35  灵敏度调整1
    CCDCLOSE,           //  36  光电载荷关机1
    CCDAWAKEN,          //  37  光电载荷唤醒1
    IROPEN,             //  38  红外电源开1
    IRCLOSE,            //  39  红外电源关1
    TAKEPHOTOCONTINUOUS,//  40  连续拍照1
};
#pragma pack(push, 1)
struct DEVICE_WORK_STATUS_TWO {
    unsigned char spare;
    unsigned char ir_main_path;
    unsigned char image_track_statu;
    unsigned char class_width_statu;
    unsigned char search_condition;
    unsigned char stick_sensitive_statu;
    unsigned char eo_work_status;
};
struct DEVICE_WORK_STATUS_ONE {
    unsigned char ir_enhance_statu;
    unsigned char ir_big_statu;
    unsigned char ir_trans_fog_statu;
    unsigned char ir_right_image_statu;
    unsigned char ir_mix_statu;
    unsigned char track_num_statu;
    unsigned char ir_statu;
    unsigned char camare_statu;
    unsigned char image_track_statu;
};
struct DEVICE_CHECK_STATUS_TWO {
    unsigned char spare;
    unsigned char sensor_control_status;
    unsigned char pitch_top_status;
    unsigned char postion_top_status;
    unsigned char in_pitch_status;
    unsigned char in_postion_status;
    unsigned char out_pitch_status;
    unsigned char out_postion_status;
};
struct DEVICE_CHECK_STATUS_ONE {
    unsigned char spare;
    unsigned char eo_status;
    unsigned char control_unit_status;
    unsigned char image_track_status;
    unsigned char camera_status;
    unsigned char tv_sensor_status;
    unsigned char ir_sensor_status;
    unsigned char servo_unit_status;
};
struct EO_TELEMETRY {
    unsigned char control_type;
    unsigned char control_order;
    short az_ang;
    short ei_ang;
    unsigned short ccd_view;
    unsigned short ir_view;
    unsigned short photo_view;
    unsigned short pod_status_1;
    unsigned short pod_status_two;
    unsigned short pod_work_status;
    unsigned short pod_work_status_Two;
};
#pragma pack(pop)
/**
 * @brief 有人机工作类
 *
 * 该类负责有人机模块的核心业务逻辑，包括：
 * - UDP通信管理
 * - 串口通信管理
 * - 性能监控和统计
 */
class YouRenJiWorker : public QObject
{
    Q_OBJECT
public:

    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit YouRenJiWorker(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     * 负责清理网络连接、串口和定时器资源
     */
    ~YouRenJiWorker();

    /**
     * @brief 设置网络地址参数
     * @param zaiheSendIP 载荷发送IP
     * @param zaiheSendPort 载荷发送端口
     * @param zaiheRecvIP 载荷接收IP
     * @param zaiheRecvPort 载荷接收端口
     * @param yourenjiSendIP 有人机发送IP
     * @param yourenjiSendPort 有人机发送端口
     * @param uavRecvIP 无人机接收IP
     * @param uavRecvPort 无人机接收端口
     */
    void setAddresses(
            const QString &zaiheSendIP, const QString &zaiheSendPort,
            const QString &uavRecvIP, const QString &uavRecvPort
            );
    
    /**
     * @brief 设置串口参数
     * @param portName 串口名称
     * @param baudRate 波特率
     * @param dataBits 数据位
     * @param parity 校验位
     * @param stopBits 停止位
     */
    void setSerialPortSettings(
            const QString &portName,
            int baudRate,
            QSerialPort::DataBits dataBits,
            QSerialPort::Parity parity,
            QSerialPort::StopBits stopBits
            );
    
    /**
     * @brief 设置消息采样率
     * @param rate 采样率（1/N，N为正整数）
     */
    void setMessageSamplingRate(int rate);

    /**
     * @brief 设置日志采样率
     * @param rate 采样率（1/N，N为正整数）
     */
    void setLogSamplingRate(int rate);

public slots:
    /**
     * @brief 启动工作线程
     * 初始化网络连接、串口和定时器
     */
    void start();

    /**
     * @brief 停止工作线程
     * 清理网络连接、串口和定时器
     */
    void stop();

    /**
     * @brief 启动串口
     * 使用当前设置打开串口
     */
    void startSerialPort();

    /**
     * @brief 停止串口
     * 关闭当前打开的串口
     */
    void stopSerialPort();
    
    /**
     * @brief 处理无人机UDP数据
     */
    void processUavUdpData();

    /**
     * @brief 处理串口数据
     * 读取串口数据并转发到载荷组播地址
     */
    void processSerialData();

    /**
     * @brief 处理串口数据
     * 读取串口数据并转发到载荷组播地址
     */
    void send_YK(const int& cmdNum,const double&cmdValue1, const double& cmdValue2, const double& cmdValue3, const double& cmdValue4, const double& cmdValue5);

    /**
     * @brief 发送数据到串口
     * @param data 待发送的数据
     */
    void sendToSerial(const QByteArray &data);
    
    /**
     * @brief 刷新缓冲数据
     */
    void flushBufferedData();

signals:
    void startBuffer();
    void startUDP();
    void startSerial();
    /**
     * @brief 消息接收信号
     * @param message 接收到的消息
     */
    void messageReceived(const QString &message);

    /**
     * @brief 错误发生信号
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);

    /**
     * @brief 连接状态变更信号
     * @param connected 是否已连接
     */
    void connectionStatusChanged(bool connected);

    /**
     * @brief 性能统计更新信号
     * @param messagesPerSecond 每秒消息数
     * @param dataBytes 每秒数据字节数
     */
    void throughputUpdated(int messagesPerSecond, int dataBytes);

    /**
     * @brief 串口状态变更信号
     * @param opened 是否已打开
     */
    void serialPortStatusChanged(bool opened);

    /**
     * @brief 串口错误信号
     * @param errorMessage 错误信息
     */
    void serialErrorOccurred(const QString &errorMessage);

private:
    void createSubThread(QThread** thread, Helper** helper, const QString &name) {
        *thread = new QThread(this);
        *helper = new Helper();

        (*helper)->moveToThread(*thread);
        if (name=="fun1"){
            connect(*helper, &Helper::execute, this, &YouRenJiWorker::flushBufferedData);
        } else if (name == "fun2"){
            connect(*helper, &Helper::execute, this, &YouRenJiWorker::processSerialData);
        } else if (name == "fun3"){
            connect(*helper, &Helper::execute, this, &YouRenJiWorker::processUavUdpData);
        }

        connect(*thread, &QThread::finished, *helper, &QObject::deleteLater);
    connect(*thread, &QThread::finished, *thread, &QObject::deleteLater);

    (*thread)->start();
    }

    void stopSubThreads(){
        if (fun1 && fun1->isRunning()){
            fun1->quit();
            fun1->wait();
        }
        if (fun2 && fun2->isRunning()){
            fun2->quit();
            fun2->wait();
        }
        if (fun3 && fun3->isRunning()){
            fun3->quit();
            fun3->wait();
        }
    }
    /**
     * @brief 初始化UDP套接字
     */
    void initUdpSockets();

    /**
     * @brief 初始化串口
     */
    void initSerialPort();

    /**
     * @brief 清理资源
     */
    void cleanupResources();

    /**
     * @brief 检查是否需要记录日志
     * @return 是否记录日志
     */
    bool shouldLogMessage();

    /**
     * @brief 发送日志消息
     * @param message 日志消息
     */
    void emitLogMessage(const QString &message);

    /**
     * @brief 发送错误消息
     * @param message 错误消息
     */
    void emitErrorMessage(const QString &message);

    /**
     * @brief 验证串口数据包完整性
     * @param data 数据包
     * @return 是否有效
     */
    bool validateSerialPacket(const QByteArray &data);

    /**
     * @brief 计算校验和
     * @param data 数据
     * @param start 起始位置
     * @param end 结束位置
     * @return 校验和
     */
    uint8_t calculateChecksum(const QByteArray &data, int start, int end);

public:
    QThread* fun1 = nullptr;
    QThread* fun2 = nullptr;
    QThread* fun3 = nullptr;
    Helper* helper1 = nullptr;
    Helper* helper2 = nullptr;
    Helper* helper3 = nullptr;
    // UDP套接字
    QUdpSocket *zaiheUdpSend;      ///< 载荷发送UDP套接字
    QUdpSocket *uavUdpRecv;        ///< 无人机接收UDP套接字
    
    // 串口
    QSerialPort *serialPort;        ///< 串口对象
    QString serialPortName;         ///< 串口名称
    int serialBaudRate;            ///< 波特率
    QSerialPort::DataBits serialDataBits;  ///< 数据位
    QSerialPort::Parity serialParity;      ///< 校验位
    QSerialPort::StopBits serialStopBits;  ///< 停止位
    bool serialPortOpen;           ///< 串口打开状态
    
    // 连接参数
    QString yourenjiRecvIP;        ///< 有人机接收IP
    QString yourenjiRecvPort;      ///< 有人机接收端口
    QString zaiheSendIP;           ///< 载荷发送IP
    QString zaiheSendPort;         ///< 载荷发送端口
    QString uavRecvIP;             ///< 无人机接收IP
    QString uavRecvPort;           ///< 无人机接收端口
    
    // 状态追踪
    bool isRunning;                ///< 运行状态
    QMutex stateMutex;            ///< 状态互斥锁
    
    // 性能监控
    QTimer *bufferFlushTimer;      ///< 缓冲刷新定时器
    int messageCount;              ///< 消息计数
    int dataBytesProcessed;        ///< 处理的数据字节数
    QElapsedTimer performanceTimer; ///< 性能计时器
    
    // 消息采样
    int messageSamplingRate;       ///< 消息采样率
    int logSamplingRate;           ///< 日志采样率
    int messageCounter;            ///< 消息计数器

    
    // 缓冲区
    QQueue<QByteArray> serialDataQueue;   ///< 串口数据队列
    QMutex bufferMutex;                  ///< 缓冲区互斥锁
    
    // 常量 - 优化后的参数
    static const int MAX_QUEUE_SIZE = 1000;                  ///< 最大队列大小
    static const int BUFFER_FLUSH_INTERVAL_MS = 5;          ///< 缓冲刷新间隔（毫秒）- 减少延迟
    static const int BUFFER_FLUSH_INTERVAL_FAST_MS = 2;     ///< 快速刷新间隔（毫秒）- 高负载时使用
    static const int THROUGHPUT_UPDATE_INTERVAL_MS = 1000;  ///< 性能统计更新间隔（毫秒）
    static const int MAX_SERIAL_BUFFER_SIZE = 32768;        ///< 最大串口缓冲区大小 - 增加缓冲
    static const int SERIAL_READ_TIMEOUT_MS = 10;           ///< 串口读取超时（毫秒）
    static const int UDP_BUFFER_SIZE = 32 * 1024 * 1024;    ///< UDP缓冲区大小（32MB）

    // 光电状态信息
    struct EO_TELEMETRY eo_info;
public:
    int logCounter;               ///< 日志计数器
    bool serialAlign;
    QByteArray header1,header2;

    // 错误统计和恢复
    int serialErrorCount;         ///< 串口错误计数
    int udpErrorCount;           ///< UDP错误计数
    int dataValidationErrors;    ///< 数据验证错误计数
    QElapsedTimer lastErrorTime; ///< 最后错误时间
};

#endif // YOURENJIWORKER_H
