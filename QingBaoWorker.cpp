﻿#include "QingBaoWorker.h"
#include <QHostAddress>
#include <QNetworkDatagram>
#include <QJsonDocument>
#include <QJsonObject>
#include <QImage>
#include <QBuffer>
#include <QRunnable>
#include <QCoreApplication>
#include <QDateTime>
#include <QProcess>
#include <QStandardPaths>
#include <QTemporaryDir>
#include <algorithm>
#include <iostream>

#define PI 3.14159265358979

// 数据包构造函数实现
DataPacket::DataPacket(const QByteArray& rawData)
{
    // 尝试解析JSON
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(rawData, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        type = UNKNOWN;
        data = rawData;
        return;
    }
    
    // 成功解析JSON
    type = JSON;
    jsonData = doc.object();
    
    // 获取文件路径
    if (jsonData.contains("IMGDATA")) {
        filePath = jsonData["IMGDATA"].toString().toLower();
    }
    
    // 获取类型
    if (jsonData.contains("IMGTYPE")) {
        QString typeStr = jsonData["IMGTYPE"].toString().toLower();
        if (typeStr == "image") {
            type = IMAGE;
            planinfo.longitude = jsonData["PLANELON"].toDouble() ;
            planinfo.latitude = jsonData["PLANELON"].toDouble() ;
            planinfo.height = jsonData["PLANEHEIGHT"].toDouble();
            planinfo.pitch = jsonData["PLANEPITCH"].toDouble();
            planinfo.roll = jsonData["PLANEROLL"].toDouble();
            planinfo.yaw = jsonData["PLANEYAW"].toDouble();
            planinfo.loadPitch = jsonData["LOADPITCH"].toDouble();
            planinfo.loadYaw = jsonData["LOADYAW"].toDouble();
            planinfo.loadFov = jsonData["LOADFOV"].toDouble();
        } else if (typeStr == "ccd") {
            type = CCD;
        } else if (typeStr == "ir") {
            type = IR;
        }
    }
}

QingBaoWorker::QingBaoWorker(QObject *parent)
    : QObject(parent),
      udpSocket(nullptr),
      targetSocket(nullptr),
      imageSocket(nullptr),
      ccdSocket(nullptr),
      irSocket(nullptr),
      multicastPort(0),
      targetPort(0),
      imagePort(0),
      ccdPort(0),
      irPort(0),
      running(false),
      statsTimer(nullptr),
      samplingRate(1),
      logSamplingRate(10),
      messageCounter(0),
      logCounter(0),
      gpuAcceleration(false)
{
    // 创建所有UDP Socket
    udpSocket = new QUdpSocket(this);
    targetSocket = new QUdpSocket(this);
    imageSocket = new QUdpSocket(this);
    ccdSocket = new QUdpSocket(this);
    irSocket = new QUdpSocket(this);
    
    // 创建统计定时器
    statsTimer = new QTimer(this);
    connect(statsTimer, &QTimer::timeout, this, &QingBaoWorker::updatePerformanceStats);
    statsTimer->setInterval(1000); // 每秒更新一次统计数据
    
    // 创建统计定时器
    dataIrBufferTimer = new QTimer(this);
    connect(dataIrBufferTimer, &QTimer::timeout, this, &QingBaoWorker::sendIrDataBuffer);
    dataIrBufferTimer->setInterval(0); // 每秒更新一次统计数据

    // 创建统计定时器
    dataCCdBufferTimer = new QTimer(this);
    connect(dataCCdBufferTimer, &QTimer::timeout, this, &QingBaoWorker::sendCCdDataBuffer);
    dataCCdBufferTimer->setInterval(0); // 每秒更新一次统计数据

    // 初始化FFmpeg
    initializeFFmpeg();
    
    // 检查GPU加速是否可用
    gpuAcceleration = checkGpuAcceleration();
}

QingBaoWorker::~QingBaoWorker()
{
    stop();
    
    // 清理资源
    delete udpSocket;
    delete targetSocket;
    delete imageSocket;
    delete ccdSocket;
    delete irSocket;
}

void QingBaoWorker::setMulticastAddress(const QString &address)
{
    // 解析地址和端口
    QStringList parts = address.split(":");
    if (parts.size() == 2) {
        multicastAddress = parts[0];
        multicastPort = parts[1].toInt();
    }
}

void QingBaoWorker::setTargetAddress(const QString &address)
{
    // 解析地址和端口
    QStringList parts = address.split(":");
    if (parts.size() == 2) {
        targetHost = parts[0];
        targetPort = parts[1].toInt();
    }
}

void QingBaoWorker::setImageTargetAddress(const QString &address)
{
    // 解析地址和端口
    QStringList parts = address.split(":");
    if (parts.size() == 2) {
        imageHost = parts[0];
        imagePort = parts[1].toInt();
    }
}

void QingBaoWorker::setCcdTargetAddress(const QString &address)
{
    // 解析地址和端口
    QStringList parts = address.split(":");
    if (parts.size() == 2) {
        ccdHost = parts[0];
        ccdPort = parts[1].toInt();
    }
}

void QingBaoWorker::setIrTargetAddress(const QString &address)
{
    // 解析地址和端口
    QStringList parts = address.split(":");
    if (parts.size() == 2) {
        irHost = parts[0];
        irPort = parts[1].toInt();
    }
}

void QingBaoWorker::setSamplingRate(int rate)
{
    samplingRate = qMax(1, rate);
}

void QingBaoWorker::setLogSamplingRate(int rate)
{
    logSamplingRate = qMax(1, rate);
}

void QingBaoWorker::start()
{
    // 如果已经在运行，直接返回
    if (running) {
        return;
    }
    
    // 尝试初始化UDP Socket
    if (!initializeUdpSocket()) {
        return;
    }
    
    // 设置运行标志
    running = true;
    
    // 重置统计数据
    messageCount.storeRelease(0);
    processedCount.storeRelease(0);
    {
        QMutexLocker locker(&statsMutex);
        processingTimes.clear();
    }
    
    // 启动统计定时器
    statsTimer->start();
    dataIrBufferTimer->start();
    dataCCdBufferTimer->start();
    // 启动工作线程
    int numThreads = QThread::idealThreadCount();
    if (numThreads <= 0) numThreads = DEFAULT_NUM_THREADS;
    
    // 限制线程数量在合理范围内
    numThreads = qMin(numThreads, 8);
    
    for (int i = 0; i < numThreads; ++i) {
        QThread* thread = new QThread();
        workerThreads.append(thread);
        
        thread->start();
        
        // 创建一个Lambda函数作为线程的执行函数
        QObject* context = new QObject();
        context->moveToThread(thread);
        
        connect(thread, &QThread::finished, context, &QObject::deleteLater);
        connect(thread, &QThread::started, context, [this, context]() {
            // 工作线程函数
            while (running) {
                this->consumerThreadFunction();
            }
            
            // 线程结束时自动删除上下文对象
            context->deleteLater();
        });
    }
    
    // 启动计时器
    elapsedTimer.start();
    
    // 发送连接状态变化信号
    emit connectionStatusChanged(true);
    
    // 记录启动日志
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString logMsg = QString("[%1] <span class='system'>[系统] 工作线程已启动，线程数: %2</span>")
            .arg(timestamp)
            .arg(numThreads);
    //    if (gpuAcceleration) {
    //        logMsg += QString("<span class='system'> [GPU加速已启用]</span>");
    //    } else {
    //        logMsg += QString("<span class='system'> [使用CPU处理]</span>");
    //    }
    emit messageReceived(logMsg);
}

void QingBaoWorker::stop()
{
    // 如果没有运行，直接返回
    if (!running) {
        return;
    }
    
    // 设置停止标志
    running = false;
    
    // 唤醒所有等待的线程
    {
        QMutexLocker locker(&queueMutex);
        queueNotEmpty.wakeAll();
    }
    
    // 停止统计定时器
    statsTimer->stop();
    dataIrBufferTimer->stop();
    dataCCdBufferTimer->stop();
    // 停止并等待所有工作线程结束
    for (QThread* thread : workerThreads) {
        thread->quit();
        thread->wait();
        delete thread;
    }
    workerThreads.clear();
    
    // 断开UDP套接字
    udpSocket->close();
    
    // 清空队列
    {
        QMutexLocker locker(&queueMutex);
        packetQueue.clear();
    }
    
    // 发送连接状态变化信号
    emit connectionStatusChanged(false);
    
    // 记录停止日志
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    emit messageReceived(QString("[%1] <span class='system'>[系统] 工作线程已停止</span>")
                         .arg(timestamp));
}

void QingBaoWorker::onUdpReadyRead()
{
    // 读取所有可用的数据报
    while (udpSocket->hasPendingDatagrams()) {
        // 采样率控制
        int counter = messageCounter.fetchAndAddRelaxed(1) % samplingRate;
        if (counter != 0) {
            // 丢弃这个数据包，不进行处理
            udpSocket->receiveDatagram();
            continue;
        }
        
        QNetworkDatagram datagram = udpSocket->receiveDatagram();
        if (datagram.isValid()) {
            // 创建数据包对象
            DataPacket packet(datagram.data());
            
            // 递增消息计数器
            messageCount.fetchAndAddRelaxed(1);
            
            // 添加到处理队列
            {
                QMutexLocker locker(&queueMutex);
                
                // 如果队列已满，移除最早的数据包
                if (packetQueue.size() >= MAX_QUEUE_SIZE) {
                    packetQueue.dequeue();
                    logError("警告: 处理队列已满，丢弃最早的数据包");
                }
                
                packetQueue.enqueue(packet);
                queueNotEmpty.wakeOne();
            }
        }
    }
}

void QingBaoWorker::updatePerformanceStats()
{
    // 计算每秒处理的消息数
    int messagesPerSecond = processedCount.fetchAndStoreRelaxed(0);
    
    // 计算平均处理时间
    double avgProcessTime = 0.0;
    {
        QMutexLocker locker(&statsMutex);
        if (!processingTimes.isEmpty()) {
            double totalTime = 0.0;
            for (double time : processingTimes) {
                totalTime += time;
            }
            avgProcessTime = totalTime / processingTimes.size();
            processingTimes.clear();
        }
    }
    
    // 发送性能更新信号
    emit performanceUpdated(messagesPerSecond, avgProcessTime);
}

bool QingBaoWorker::initializeUdpSocket()
{
    // 确保有效的组播地址和端口
    if (multicastAddress.isEmpty() || multicastPort <= 0) {
        logError("无效的组播地址或端口");
        return false;
    }
    
    // 关闭现有连接
    udpSocket->close();
    
    // 绑定到任意地址，指定端口
    if (!udpSocket->bind(QHostAddress::AnyIPv4, multicastPort, QUdpSocket::ShareAddress)) {
        logError("无法绑定到端口 " + QString::number(multicastPort) + ": " + udpSocket->errorString());
        return false;
    }
    
    // 加入组播组
    if (!udpSocket->joinMulticastGroup(QHostAddress(multicastAddress))) {
        logError("无法加入组播组 " + multicastAddress + ": " + udpSocket->errorString());
        return false;
    }
    
    // 连接readyRead信号
    connect(udpSocket, &QUdpSocket::readyRead, this, &QingBaoWorker::onUdpReadyRead, Qt::DirectConnection);
    
    // 记录成功日志
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    emit messageReceived(QString("[%1] <span class='system'>[系统] 已连接到组播地址 %2:%3</span>")
                         .arg(timestamp)
                         .arg(multicastAddress)
                         .arg(multicastPort));
    
    return true;
}

void QingBaoWorker::initializeFFmpeg()
{
  
    // 注册所有编解码器和格式
#if LIBAVCODEC_VERSION_INT < AV_VERSION_INT(58, 9, 100)
    av_register_all();
#endif
}

bool QingBaoWorker::checkGpuAcceleration()
{
    // 检查是否支持硬件加速编码
    const AVCodec *codec = avcodec_find_encoder_by_name("h264_nvenc");  // NVIDIA GPU加速
    if (!codec) {
        codec = avcodec_find_encoder_by_name("h264_qsv");  // Intel Quick Sync Video
    }
    if (!codec) {
        codec = avcodec_find_encoder_by_name("h264_vaapi");  // VA-API
    }
    if (!codec) {
        codec = avcodec_find_encoder_by_name("h264_amf");  // AMD AMF
    }
    
    return codec != nullptr;
}

void QingBaoWorker::consumerThreadFunction()
{
    DataPacket packet;
    bool hasPacket = false;
    
    // 从队列中获取数据包
    {
        QMutexLocker locker(&queueMutex);
        while (running && packetQueue.isEmpty()) {
            // 等待队列非空信号
            queueNotEmpty.wait(&queueMutex);
            
            // 如果停止运行，返回
            if (!running) {
                return;
            }
        }
        
        // 获取数据包
        if (!packetQueue.isEmpty()) {
            packet = packetQueue.dequeue();
            hasPacket = true;
        }
    }
    
    // 处理数据包
    if (hasPacket) {
        QElapsedTimer processTimer;
        processTimer.start();
        
        // 处理数据包
        processPacket(packet);
        
        // 记录处理时间
        double elapsedMs = processTimer.elapsed();
        {
            QMutexLocker locker(&statsMutex);
            if (processingTimes.size() >= MAX_TIME_SAMPLES) {
                processingTimes.dequeue();
            }
            processingTimes.enqueue(elapsedMs);
        }
        
        // 增加已处理消息计数
        processedCount.fetchAndAddRelaxed(1);
    }
}

void QingBaoWorker::processPacket(const DataPacket &packet)
{
    // 根据数据包类型进行处理
    bool processed = false;
    QString typeStr;
    
    switch (packet.type) {
    case DataPacket::IMAGE:
        typeStr = "图像";
        processed = processImage(packet);
        break;
    case DataPacket::CCD:
        typeStr = "CCD视频";
        processed = processCcdVideo(packet);
        break;
    case DataPacket::IR:
        typeStr = "IR视频";
        processed = processIrVideo(packet);
        break;
    case DataPacket::JSON:
        typeStr = "JSON";
        // 转发原始JSON数据
        if (targetPort > 0) {
            QJsonDocument doc(packet.jsonData);
            QByteArray jsonData = doc.toJson(QJsonDocument::Compact);
            processed = sendPacket(targetSocket, targetHost, targetPort, jsonData, "JSON");
        }
        break;
    default:
        typeStr = "未知";
        // 转发原始数据
        if (targetPort > 0) {
            processed = sendPacket(targetSocket, targetHost, targetPort, packet.data, "default");
        }
    }
    
    // 记录处理日志
    int counter = logCounter.fetchAndAddRelaxed(1) % logSamplingRate;
    if (counter == 0) {
        QString path = packet.filePath.isEmpty() ? "无文件路径" : packet.filePath;
        QString message;
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
        
        if (processed) {
            message = QString("[%1] <span class='data'>[处理] 类型: %2, 处理%3, 路径: <span class='file'>%4</span></span>")
                    .arg(timestamp)
                    .arg(typeStr)
                    .arg(processed ? "成功" : "失败")
                    .arg(path);
        } else {
            message = QString("[%1] <span class='error'>[失败] 类型: %2, 处理失败, 路径: <span class='file'>%4</span></span>")
                    .arg(timestamp)
                    .arg(typeStr)
                    .arg(path);
        }
        
        logMessage(message);
    }
}

bool QingBaoWorker::processImage(const DataPacket &packet)
{
    // 检查文件路径是否存在
    if (packet.filePath.isEmpty()) {
        logError("图像处理错误: 文件路径为空");
        return false;
    }
    
    // 检查是否有有效的目标地址
    if (imageHost.isEmpty() || imagePort <= 0) {
        logError("图像处理错误: 无效的目标地址");
        return false;
    }
    
    // 打开并读取图像文件
    QFile file(packet.filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        logError("图像处理错误: 无法打开文件 " + packet.filePath + ": " + file.errorString());
        return false;
    }
    // 读取文件内容
    QByteArray imageData = file.readAll();

    file.close();
    
    // 在这里调用库，进行矫正定位，然后将data数据与矫正数据合并
    INPUT_TEST Para = { 0, packet.planinfo.longitude, packet.planinfo.latitude, packet.planinfo.height, \
                        packet.planinfo.pitch, packet.planinfo.roll, packet.planinfo.yaw, packet.planinfo.loadPitch, \
                        packet.planinfo.loadYaw, packet.planinfo.loadFov, 100 };
    OUTPUT_TEST outPara = { 1 };
    CORRECTION_PARAM* pstu;
    pstu = reinterpret_cast<CORRECTION_PARAM*>(&outPara);
    int resultnum = jz.getImageLocationInfo(Para, pstu);
    if(-1 == resultnum){
        logError("矫正失败");
        return false;
    }
    save_dat.correction_param = *pstu;
    save_dat.msgh.message_code_uh = 2000;

    uint64_t timestamp = static_cast<uint64_t>(QDateTime::currentMSecsSinceEpoch());
    save_dat.msgh.timestamp_ul = timestamp;
    save_dat.data = std::vector<unsigned char>(imageData.begin(), imageData.end());

    save_dat.package_length_ul = sizeof(CORRECTION_PARAM) + imageData.size();
    save_dat.package_number_uh = 1;
    //当前图片个数
    save_dat.photo_number_ul = ++photo_count;
    save_dat.plat_id_uh = 63;
    //gps时间
    save_dat.time_of_update_ul = 1000;
    save_dat.total_ackage_number_uh=1;
    //    //     1. 创建一个 QByteArray 用于存储序列化的数据
    //    QByteArray byteArray;

    //    //     2. 将结构体中的每个字段序列化
    //    QDataStream outStream(&byteArray, QIODevice::WriteOnly);

    //    // 序列化 MDS_HEADER
    //    outStream << save_dat.msgh.message_code_uh
    //              << save_dat.msgh.message_length_uh
    //              << save_dat.msgh.timestamp_ul
    //              << save_dat.msgh.message_number_uh
    //              << save_dat.msgh.message_priority_e
    //              << save_dat.msgh.spare;

    //    // 序列化其他基本字段
    //    outStream << save_dat.plat_id_uh
    //              << save_dat.photo_number_ul
    //              << save_dat.time_of_update_ul
    //              << save_dat.total_ackage_number_uh
    //              << save_dat.package_number_uh
    //              << save_dat.package_length_ul;

    //    // 序列化 CORRECTION_PARAM
    //    outStream.writeRawData(reinterpret_cast<char*>(&save_dat.correction_param), sizeof(CORRECTION_PARAM));

    //    // 序列化 std::vector<unsigned char> 数据
    //    outStream.writeRawData(reinterpret_cast<char*>(save_dat.data.data()), save_dat.data.size());

    // 保存到本地文件（新增代码）

    QFile file1("encoded_data.data");
    if (file1.open(QIODevice::WriteOnly | QIODevice::Truncate | QIODevice::Append)) {  // 追加模式写入
        file1.write(reinterpret_cast<const char*>(&save_dat.msgh.message_code_uh), sizeof(save_dat.msgh.message_code_uh));
        file1.write(reinterpret_cast<const char*>(&save_dat.msgh.message_length_uh), sizeof(save_dat.msgh.message_length_uh));
        file1.write(reinterpret_cast<const char*>(&save_dat.msgh.timestamp_ul), sizeof(save_dat.msgh.timestamp_ul));
        file1.write(reinterpret_cast<const char*>(&save_dat.msgh.message_number_uh), sizeof(save_dat.msgh.message_number_uh));
        file1.write(reinterpret_cast<const char*>(&save_dat.msgh.message_priority_e), sizeof(save_dat.msgh.message_priority_e));
        file1.write(reinterpret_cast<const char*>(&save_dat.msgh.spare), sizeof(save_dat.msgh.spare));
        file1.write(reinterpret_cast<const char*>(&save_dat.plat_id_uh), sizeof(save_dat.plat_id_uh));
        file1.write(reinterpret_cast<const char*>(&save_dat.photo_number_ul), sizeof(save_dat.photo_number_ul));
        file1.write(reinterpret_cast<const char*>(&save_dat.time_of_update_ul), sizeof(save_dat.time_of_update_ul));
        file1.write(reinterpret_cast<const char*>(&save_dat.total_ackage_number_uh), sizeof(save_dat.total_ackage_number_uh));
        file1.write(reinterpret_cast<const char*>(&save_dat.package_number_uh), sizeof(save_dat.package_number_uh));
        file1.write(reinterpret_cast<const char*>(&save_dat.package_length_ul), sizeof(save_dat.package_length_ul));
        file1.write(reinterpret_cast<const char*>(&save_dat.correction_param), sizeof(save_dat.correction_param));
        file1.write(reinterpret_cast<const char*>(save_dat.data.data()), save_dat.data.size());
        file1.close();
    } else {
        logError("无法保存编码数据到文件: " + file1.errorString());
    }

    QFile file2("encoded_data.data");
    if (!file2.open(QIODevice::ReadOnly)) {
        logError("图像处理错误: 无法打开文件 " + packet.filePath + ": " + file.errorString());
        return false;
    }
    // 读取文件内容
    QByteArray tmp = file2.readAll();

    file2.close();
    // 发送图像数据
    bool success = sendPacket(imageSocket, imageHost, imagePort, tmp ,"image");
    
    return success;
}

bool QingBaoWorker::processCcdVideo(const DataPacket &packet)
{
    // 检查文件路径是否存在
    if (packet.filePath.isEmpty()) {
        logError("CCD视频处理错误: 文件路径为空");
        return false;
    }
    
    // 检查是否有有效的目标地址
    if (ccdHost.isEmpty() || ccdPort <= 0) {
        logError("CCD视频处理错误: 无效的目标地址");
        return false;
    }
    
    // 加载图像数据
    QImage image(packet.filePath);
    if (image.isNull()) {
        logError("CCD视频处理错误: 无法加载图像 " + packet.filePath);
        return false;
    }
    
    // 转换为RGB格式
    image = image.convertToFormat(QImage::Format_RGB888);
    
    // 创建编码器上下文
    std::unique_ptr<EncoderContext> encoderCtx = createEncoder(image.width(), image.height(), true);
    if (!encoderCtx || !encoderCtx->codecContext) {
        logError("CCD视频处理错误: 无法创建编码器");
        return false;
    }
    
    // 将QImage转换为字节数组
    QByteArray imageBytes;
    {
        QBuffer buffer(&imageBytes);
        buffer.open(QIODevice::WriteOnly);
        image.save(&buffer, "JPEG", 90);
    }
    
    // 编码帧
    QByteArray encodedData;
    if (!encodeFrame(encoderCtx, imageBytes, encodedData)) {
        logError("CCD视频处理错误: 编码失败");
        return false;
    }
    
    // 发送编码后的视频数据
    bool success = sendPacket(ccdSocket, ccdHost, ccdPort, encodedData, "ccd");
    
    return success;
}

bool QingBaoWorker::processIrVideo(const DataPacket &packet)
{
    // 检查文件路径是否存在
    if (packet.filePath.isEmpty()) {
        logError("IR视频处理错误: 文件路径为空");
        return false;
    }
    
    // 检查是否有有效的目标地址
    if (irHost.isEmpty() || irPort <= 0) {
        logError("IR视频处理错误: 无效的目标地址");
        return false;
    }
    
    // 加载图像数据
    QImage image(packet.filePath);
    if (image.isNull()) {
        logError("IR视频处理错误: 无法加载图像 " + packet.filePath);
        return false;
    }
    
    // 转换为RGB格式
    image = image.convertToFormat(QImage::Format_RGB888);
    
    // 创建编码器上下文
    std::unique_ptr<EncoderContext> encoderCtx = createEncoder(image.width(), image.height(), false);
    if (!encoderCtx || !encoderCtx->codecContext) {
        logError("IR视频处理错误: 无法创建编码器");
        return false;
    }
    
    // 将QImage转换为字节数组
    QByteArray imageBytes;
    {
        QBuffer buffer(&imageBytes);
        buffer.open(QIODevice::WriteOnly);
        image.save(&buffer, "JPEG", 90);
    }
    
    // 编码帧
    QByteArray encodedData;
    if (!encodeFrame(encoderCtx, imageBytes, encodedData)) {
        logError("IR视频处理错误: 编码失败");
        return false;
    }
    
    // 发送编码后的视频数据
    bool success = sendPacket(irSocket, irHost, irPort, encodedData, "ir");
    
    return success;
}

std::unique_ptr<EncoderContext> QingBaoWorker::createEncoder(int width, int height, bool isCcd)
{
    // 强制使用软件编码器
    const AVCodec *codec = avcodec_find_encoder(AV_CODEC_ID_H264);
    if (!codec) {
        logError("无法找到H.264软件编码器");
        return nullptr;
    }
    //    logMessage("使用软件编码器: " + QString(codec->name));
    
    std::unique_ptr<EncoderContext> ctx(new EncoderContext());
    
    // 创建编码器上下文
    ctx->codecContext = avcodec_alloc_context3(codec);
    if (!ctx->codecContext) {
        logError("无法分配编码器上下文");
        return nullptr;
    }
    
    // 设置编码器参数
    ctx->codecContext->width = width;
    ctx->codecContext->height = height;
    // 使用兼容性更好的AVRational初始化方式
    AVRational timeBase = {1, 25};
    AVRational frameRate = {25, 1};
    ctx->codecContext->time_base = timeBase;
    ctx->codecContext->framerate = frameRate;
    ctx->codecContext->pix_fmt = AV_PIX_FMT_YUV420P;
    ctx->codecContext->max_b_frames = 0;        // 不使用B帧，降低延迟
    // 优化参数
    ctx->codecContext->gop_size = 20;//原来是10
    ctx->codecContext->qmin = 18;
    ctx->codecContext->qmax = 28;
    ctx->codecContext->max_qdiff = 3;
    //    ctx->codecContext->qcompress = 0.6f;

    int pixels = width * height;
    // 设置比特率
    if (isCcd) {
        if (pixels > 1920 * 1080){
            ctx->codecContext->bit_rate = 4000000;  // 4 Mbps
        }else if (pixels > 1280 * 720){
            ctx->codecContext->bit_rate = 1500000;  // 1.5 Mbps
        }else{
            ctx->codecContext->bit_rate = 1000000;  // 1 Mbps
        }
    } else {
        if (pixels > 1920 * 1080){
            ctx->codecContext->bit_rate = 2000000;  // 2 Mbps
        }else if (pixels > 1280 * 720){
            ctx->codecContext->bit_rate = 1200000;  // 1.2 Mbps
        }else{
            ctx->codecContext->bit_rate = 500000;  // 0.5 Mbps
        }
    }
    
    // 设置编码器选项 - 使用最小必要的选项集
    AVDictionary *options = nullptr;
    
    // 只设置最基本的选项，避免使用可能不兼容的profile参数
    if (strstr(codec->name, "libx264")) {
        // 只设置预设和速度参数
        av_dict_set(&options, "preset", "ultrafast", 0);
        av_dict_set(&options, "tune", "zerolatency", 0);
        // 启用profile和level
        av_dict_set(&options, "coder", "1", 0);
        //        av_dict_set(&options, "profile", "main", 0);
        av_dict_set(&options, "partitions", "+parti8x8+parti4x4+partp8x8+partb8x8", 0);
        av_dict_set(&options, "me_method", "hex", 0);
        av_dict_set(&options, "refs", "3", 0);
    }
    
    // 打开编码器 - 没有设置选项
    int ret = avcodec_open2(ctx->codecContext, codec, &options);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        logError(QString("无法打开编码器: %1").arg(errbuf));
        
        // 如果依然失败，尝试没有任何选项的方式
        if (options) {
            av_dict_free(&options);
            options = nullptr;
            av_dict_set(&options, "preset", "ultrafast", 0);
            av_dict_set(&options, "tune", "zerolatency", 0);
            ret = avcodec_open2(ctx->codecContext, codec, nullptr);
            if (ret < 0) {
                av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
                logError(QString("再次尝试无选项打开编码器失败: %1").arg(errbuf));
                return nullptr;
            } else {
                logMessage("成功以无选项方式打开编码器");
            }
        } else {
            return nullptr;
        }
    }
    
    // 释放选项字典
    if (options) {
        av_dict_free(&options);
    }
    
    // 分配帧
    ctx->frame = av_frame_alloc();
    if (!ctx->frame) {
        logError("无法分配帧");
        return nullptr;
    }
    
    ctx->frame->format = ctx->codecContext->pix_fmt;
    ctx->frame->width = ctx->codecContext->width;
    ctx->frame->height = ctx->codecContext->height;
    
    // 分配帧缓冲区
    ret = av_frame_get_buffer(ctx->frame, 32);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        logError(QString("无法分配帧缓冲区: %1").arg(errbuf));
        return nullptr;
    }
    
    //    logMessage("成功创建编码器");
    return ctx;
}

bool QingBaoWorker::encodeFrame(std::unique_ptr<EncoderContext> &encoderCtx, const QByteArray &imageData, QByteArray &outPacket)
{
    // 加载图像
    QImage image;
    if (!image.loadFromData(imageData)) {
        logError("无法从数据加载图像");
        return false;
    }
    
    // 确保图像格式正确
    image = image.convertToFormat(QImage::Format_RGB888);
    
    // 如果需要调整图像大小
    if (image.width() != encoderCtx->codecContext->width || image.height() != encoderCtx->codecContext->height) {
        image = image.scaled(encoderCtx->codecContext->width,
                             encoderCtx->codecContext->height,
                             Qt::IgnoreAspectRatio,
                             Qt::SmoothTransformation);
    }
    
    // 确保帧可写
    int ret = av_frame_make_writable(encoderCtx->frame);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        logError(QString("无法使帧可写: %1").arg(errbuf));
        return false;
    }
    
    // 创建或获取转换上下文
    if (!encoderCtx->swsContext) {
        encoderCtx->swsContext = sws_getContext(
                    image.width(), image.height(), AV_PIX_FMT_RGB24,
                    encoderCtx->codecContext->width, encoderCtx->codecContext->height, AV_PIX_FMT_YUV420P,
                    SWS_BICUBIC, nullptr, nullptr, nullptr);
        
        if (!encoderCtx->swsContext) {
            logError("无法创建SwsContext");
            return false;
        }
    }
    
    // 准备源数据
    uint8_t *srcData[4] = { const_cast<uint8_t*>(image.bits()), nullptr, nullptr, nullptr };
    int srcLinesize[4] = { image.bytesPerLine(), 0, 0, 0 };
    
    // 转换图像格式
    sws_scale(encoderCtx->swsContext, srcData, srcLinesize, 0, image.height(),
              encoderCtx->frame->data, encoderCtx->frame->linesize);
    
    // 设置帧时间戳
    encoderCtx->frame->pts = encoderCtx->frameCount++;
    
    // 发送帧到编码器
    ret = avcodec_send_frame(encoderCtx->codecContext, encoderCtx->frame);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        logError(QString("发送帧到编码器失败: %1").arg(errbuf));
        return false;
    }
    
    // 接收编码后的数据包
    AVPacket *pkt = av_packet_alloc();
    if (!pkt) {
        logError("无法分配数据包");
        return false;
    }
    
    ret = avcodec_receive_packet(encoderCtx->codecContext, pkt);
    if (ret < 0) {
        av_packet_free(&pkt);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
            // 编码器需要更多帧或已经结束
            return true;
        } else {
            char errbuf[AV_ERROR_MAX_STRING_SIZE];
            av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
            logError(QString("从编码器接收数据包失败: %1").arg(errbuf));
            return false;
        }
    }
    
    // 复制编码数据到输出数组
    outPacket = QByteArray(reinterpret_cast<const char*>(pkt->data), pkt->size);
    //    // 保存到本地文件（新增代码）
    //    QFile file("encoded_data.bin");
    //    if (file.open(QIODevice::WriteOnly | QIODevice::Append)) {  // 追加模式写入
    //        file.write(outPacket);
    //        file.close();
    //    } else {
    //        logError("无法保存编码数据到文件: " + file.errorString());
    //    }
    // 释放数据包
    av_packet_free(&pkt);
    
    return true;
}

void QingBaoWorker::sendIrDataBuffer(){
    if (dataIrBuff.empty()) return;
    QByteArray metaData = dataIrBuff.dequeue();
    qint64 bytesSent = irSocket->writeDatagram(metaData, QHostAddress(irHost), irPort);
    if (bytesSent == -1) return;
}

void QingBaoWorker::sendCCdDataBuffer(){
    if (dataCCdBuff.empty()) return;
    QByteArray metaData = dataCCdBuff.dequeue();
    qint64 bytesSent = ccdSocket->writeDatagram(metaData, QHostAddress(ccdHost), ccdPort);
    if (bytesSent == -1) return;
}

bool QingBaoWorker::sendPacket(QUdpSocket *socket, const QString &host, int port, const QByteArray &data, const QString &type, int maxPacketSize)
{
    // 检查输入参数
    if (!socket || host.isEmpty() || port <= 0 || data.isEmpty()) {
        return false;
    }
    qint64 currentMilliseconds = QDateTime::currentMSecsSinceEpoch();
    QString timestamp = QString::number(currentMilliseconds);

    // 如果数据小于最大包大小，一次性发送
    if (data.size() <= maxPacketSize) {
        QJsonObject json;
        json["currentPacket"] = 0;
        json["totalPackets"] = 1;
        json["size"] = data.size();     // 当前包大小
        json["totalSize"] = data.size();// 总大小
        json["timestamp"] = timestamp;
        json["data"] = QJsonValue::fromVariant(QVariant(data));
        json["type"] = type;
        QJsonDocument metaDoc(json);
        QByteArray metaData = metaDoc.toJson(QJsonDocument::Compact);
        qint64 bytesSent = socket->writeDatagram(metaData, QHostAddress(host), port);
        return bytesSent == data.size();
    }
    
    // 数据需要分包发送
    int totalPackets = (data.size() + maxPacketSize - 1) / maxPacketSize;
    
    for (int i = 0; i < totalPackets; ++i) {
        // 计算当前包的大小
        int offset = i * maxPacketSize;
        const int chunkSize = qMin(maxPacketSize, data.size() - offset);
        const QByteArray chunk = data.mid(offset, chunkSize);
        // 构建JSON包
        QJsonObject json;
        json["currentPacket"] = i;     // 当前包序号（从0开始）
        json["totalPackets"] = totalPackets;
        json["size"] = chunkSize;          // 当前分包大小
        json["timestamp"] = timestamp;
        json["data"] = QString(chunk.toBase64());
        json["type"] = type;
        QJsonDocument metaDoc(json);
        QByteArray metaData = metaDoc.toJson(QJsonDocument::Compact);
        if(socket == imageSocket){
            qint64 bytesSent = socket->writeDatagram(metaData, QHostAddress(host), port);
            if (bytesSent != metaData.size()) {
                logError(QString("分片 %1/%2 发送目标: %3:%4失败").arg(i).arg(totalPackets).arg(host).arg(port));
                return false;
            }
        } else if (socket == irSocket){
            // 如果队列已满，移除最早的数据包
            if (dataIrBuff.size() >= MAX_QUEUE_SIZE) {
                dataIrBuff.clear();
                logError("警告: 处理队列已满，丢弃最早的数据包");
            }
            // 将数据包放到队列里，定时器从队列取值发送
            dataIrBuff.enqueue(metaData);
        } else if (socket == ccdSocket){
            // 如果队列已满，移除最早的数据包
            if (dataCCdBuff.size() >= MAX_QUEUE_SIZE) {
                dataCCdBuff.clear();
                logError("警告: 处理队列已满，丢弃最早的数据包");
            }
            dataCCdBuff.enqueue(metaData);
        }

        // 发送数据包
        //        qint64 bytesSent = socket->writeDatagram(metaData, QHostAddress(host), port);
        //        if (bytesSent != metaData.size()) {
        //            logError(QString("分片 %1/%2 发送目标: %3:%4失败").arg(i).arg(totalPackets).arg(host).arg(port));
        //            return false;
        //        }
        //        QThread::usleep(1);
        //          // 100微秒
        //        //        // 短暂延时，避免发送过快导致丢包
        //        if (totalPackets > 100) {
        //            QThread::usleep(100);  // 100微秒
        //        }
    }
    //    QThread::usleep(100);
    // 记录日志
    if (shouldLogMessage()) {
        emit messageReceived(QString("[%1] <span class='send'>[发送] 目标: %2:%3 总大小: %4 分包: %5</span>")
                             .arg(timestamp)
                             .arg(host)
                             .arg(port)
                             .arg(data.size())
                             .arg(totalPackets));
    }
    
    return true;
}

void QingBaoWorker::logMessage(const QString &message)
{
    emit messageReceived(message);
}
bool QingBaoWorker::shouldLogMessage()
{
    return (logCounter.fetchAndAddRelaxed(1) % logSamplingRate) == 0;
}
void QingBaoWorker::logError(const QString &error)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    emit errorOccurred(QString("[%1] %2").arg(timestamp).arg(error));
}
