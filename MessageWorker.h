#ifndef MESSAGEWORKER_H
#define MESSAGEWORKER_H

#include <QObject>
#include <QWebSocket>
#include <QUdpSocket>
#include <QTimer>
#include <QQueue>
#include <QMutex>
#include <QElapsedTimer>
#include <QHash>
#include <QFile>

#pragma pack(push, 1)
/**
 * @brief 航点数据结构
 */
struct ROUTE_POINT {
    uint16_t uhRoutePointID;    ///< 航点标识
    char czRouteName[32];       ///< 航点名称
    float fLongitude;           ///< 经度（弧度）
    float fLatitude;            ///< 纬度（弧度）
    int shAltitude;           ///< 高度（米）
    uint16_t uhRouteType;
    float fTurnGrandien;
    float fTurnRadius;
    float fVelocity;
    short shArriveTimePriority;
    long IArriveTime;
    uint16_t uhValidPointNumber;
    uint16_t uhPointMissionState;
};
#pragma pack(pop)
/**
 * @brief 消息处理工作类
 * 
 * 该类负责处理导调模块的核心业务逻辑，包括：
 * - WebSocket消息的收发
 * - UDP组播数据的处理
 * - 消息批处理和性能优化
 * - 网络连接状态管理
 */
class MessageWorker : public QObject
{
    Q_OBJECT
public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit MessageWorker(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     * 负责清理网络连接和定时器资源
     */
    ~MessageWorker();

    /**
     * @brief 设置网络地址参数
     * @param dtRecvUrl 导调接收WebSocket地址
     * @param dtSndUrl 导调发送WebSocket地址
     * @param uavSndIP 无人机发送IP
     * @param uavSndPort 无人机发送端口
     * @param uavRecvIP 无人机接收IP
     * @param uavRecvPort 无人机接收端口
     * @param zhRecvIP 载荷接收IP
     * @param zhRecvPort 载荷接收端口
     */
    void setAddresses(const QString &dtRecvUrl, const QString &dtSndUrl,
                     const QString &uavSndIP, const QString &uavSndPort,
                     const QString &uavRecvIP, const QString &uavRecvPort,
                     const QString &zhRecvIP, const QString &zhRecvPort);
    
    /**
     * @brief 设置消息采样率
     * @param rate 采样率（1/N，N为正整数）
     */
    void setMessageSamplingRate(int rate);

    /**
     * @brief 设置日志采样率
     * @param rate 采样率（1/N，N为正整数）
     */
    void setLogSamplingRate(int rate);

    /**
     * @brief 设置航点文件路径
     * @param filePath 航点文件路径
     */
    void setWaypointFilePath(const QString &filePath);
public slots:
    /**
     * @brief 启动工作线程
     * 初始化网络连接和定时器
     */
    void start();

    /**
     * @brief 停止工作线程
     * 清理网络连接和定时器
     */
    void stop();

    /**
     * @brief 处理WebSocket消息
     * @param message 接收到的WebSocket消息
     */
    void processWebSocketMessage(const QString &message);

    /**
     * @brief 处理UDP消息
     */
    void processUdpMessage();

    /**
     * @brief 检查WebSocket连接状态
     * 在连接断开时尝试重连
     */
    void checkWebSocketConnection();

    /**
     * @brief 检查航线文件是否存在，
     * 并将航线信息发送至导调，删除航线文件
     */
    void checkRouteFile();
signals:
    /**
     * @brief 消息接收信号
     * @param message 接收到的消息
     */
    void messageReceived(const QString &message);

    /**
     * @brief 错误发生信号
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);

    /**
     * @brief 连接状态变更信号
     * @param connected 是否已连接
     */
    void connectionStatusChanged(bool connected);

    /**
     * @brief 性能统计更新信号
     * @param messagesPerSecond 每秒消息数
     * @param packetsPerSecond 每秒数据包数
     * @param avgLatency 平均延迟（毫秒）
     */
    void throughputUpdated(int messagesPerSecond, int packetsPerSecond, double avgLatency);

    /**
     * @brief WebSocket连接成功信号
     */
    void webSocketConnected();

    /**
     * @brief WebSocket断开连接信号
     */
    void webSocketDisconnected();

private:
    /**
     * @brief 初始化UDP组播
     * @param udpSocket UDP套接字指针
     * @param ip 组播地址
     * @param port 端口号
     */
    void initUdpMulticast(QUdpSocket *udpSocket, const QString &ip, const QString &port);

    /**
     * @brief 重连WebSocket
     */
    void reconnectWebSockets();

    /**
     * @brief 更新性能统计
     */
    void updateThroughput();

    /**
     * @brief 处理待处理消息队列
     */
    void processPendingMessages();

    /**
     * @brief 检查是否需要记录日志
     * @return 是否记录日志
     */
    bool shouldLogMessage();
    
    /**
     * @brief 发送带时间戳的日志消息
     * @param message 日志消息
     */
    void emitLogMessage(const QString &message);

    /**
     * @brief 发送带时间戳的错误消息
     * @param message 错误消息
     */
    void emitErrorMessage(const QString &message);
    
    /**
     * @brief 创建Socket对象
     */
    void createSocketObjects();

    /**
     * @brief 删除Socket对象
     */
    void deleteSocketObjects();

    /**
     * @brief 解析航点文件
     * @param filePath 文件路径
     * @return 解析的航点列表
     */
    QList<ROUTE_POINT> parseWaypointFile(const QString &filePath);

    /**
     * @brief 将度分秒转换为弧度
     * @param degrees 度
     * @param minutes 分
     * @param seconds 秒
     * @return 弧度值
     */
    double dmsToRadians(int degrees, int minutes, double seconds);
private:
    // WebSocket连接
    QWebSocket *dtWebRecv;        ///< 导调接收WebSocket
    QWebSocket *dtWebSnd;         ///< 导调发送WebSocket

    // UDP套接字
    QUdpSocket *uavUdpSnd;        ///< 无人机发送UDP套接字
    QUdpSocket *uavUdpRecv;       ///< 无人机接收UDP套接字
    QUdpSocket *zhUdpRecv;        ///< 载荷接收UDP套接字
    QUdpSocket *routeRecv;        ///< 航线接收UDP套接字

    // 连接参数
    QString dtRecvUrl;            ///< 导调接收WebSocket地址
    QString dtSndUrl;             ///< 导调发送WebSocket地址
    QString uavSndIP;             ///< 无人机发送IP
    QString uavSndPort;           ///< 无人机发送端口
    QString uavRecvIP;            ///< 无人机接收IP
    QString uavRecvPort;          ///< 无人机接收端口
    QString zhRecvIP;             ///< 载荷接收IP
    QString zhRecvPort;           ///< 载荷接收端口

    // 状态追踪
    bool isRunning;               ///< 运行状态
    bool dtWebRecvConnected;      ///< 导调接收WebSocket连接状态
    bool dtWebSndConnected;       ///< 导调发送WebSocket连接状态

    // 性能监控
    QTimer *throughputTimer;      ///< 性能统计定时器
    QTimer *batchTimer;           ///< 批处理定时器
    QTimer *reconnectTimer;       ///< 重连定时器
    QTimer *routeTimer;           ///< 航线文件计时器
    int messageCount;             ///< 消息计数
    int packetCount;             ///< 数据包计数
    int routSendCnt = 5;            ///< 航线发送计数
    double totalLatency;         ///< 总延迟
    QElapsedTimer latencyTimer;  ///< 延迟计时器
    QMutex mutex;                ///< 互斥锁

    // 消息批处理
    QHash<QObject*, QQueue<QByteArray>> messageBatches;  ///< 消息批处理队列
    static const int MAX_BATCH_SIZE = 100;               ///< 最大批处理大小
    
    // 消息采样
    int messageSamplingRate;      ///< 消息采样率
    int logSamplingRate;          ///< 日志采样率
    int messageCounter;           ///< 消息计数器


    // 消息队列
    QQueue<QByteArray> pendingMessages;                  ///< 待处理消息队列
    static const int MAX_QUEUE_SIZE = 10000;            ///< 最大队列大小
    
    // 性能设置
    static const int BATCH_INTERVAL_MS = 50;            ///< 批处理间隔（毫秒）
    static const int WEBSOCKET_CHECK_INTERVAL_MS = 5000; ///< WebSocket检查间隔（毫秒）

    // 航点文件路径
    QString waypointFilePath="";     ///< 航点文件路径

    // 无人机遥测信息头
    QString ENTITYID="1001";//ID
    QString ENTITYMODEL="005";//
    QString ENTITYTYPE="UAV";//
    QString IDENTITYFICATION="5";

public:
    int logCounter;              ///< 日志计数器
};

#endif // MESSAGEWORKER_H 
