﻿#ifndef QINGBAO_H
#define QINGBAO_H

#include <QMainWindow>
#include <QTimer>
#include <QThread>
#include <QQueue>
#include <QMutex>
#include <QUdpSocket>
#include <QLabel>
#include <QDateTime>
#include <QMessageBox>

// 前向声明
class QingBaoWorker;

namespace Ui {
class QingBao;
}

/**
 * @brief 情报处理模块主窗口类
 * 
 * 该类负责情报处理功能的实现，包括：
 * - UDP组播数据接收
 * - 文件数据读取和处理
 * - UDP数据转发
 * - 性能监控和统计
 * - 日志记录和显示
 */
class QingBao : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口指针
     */
    explicit QingBao(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     * 负责清理资源，包括工作线程、定时器等
     */
    ~QingBao();

private slots:
    /**
     * @brief 连接/断开连接按钮点击处理
     */
    void onConnect();

    /**
     * @brief 处理一般消息
     * @param message 待处理的消息
     */
    void handleMessage(const QString &message);

    /**
     * @brief 处理错误消息
     * @param error 错误信息
     */
    void handleError(const QString &error);

    /**
     * @brief 更新连接状态
     * @param connected 是否已连接
     */
    void updateConnectionStatus(bool connected);

    /**
     * @brief 更新处理性能统计
     * @param messagesPerSecond 每秒处理消息数
     * @param avgProcessTime 平均处理时间（毫秒）
     */
    void updateProcessingStats(int messagesPerSecond, double avgProcessTime);

    /**
     * @brief 更新日志显示
     * 将缓冲区中的日志消息显示到界面上
     */
    void updateLogDisplay();

private:
    Ui::QingBao *ui;                 ///< UI对象指针
    QThread workerThread;            ///< 工作线程
    QingBaoWorker *worker;           ///< 情报处理工作对象
    QTimer *logUpdateTimer;          ///< 日志更新定时器
    QQueue<QString> logBuffer;       ///< 日志缓冲区
    QMutex logMutex;                 ///< 日志互斥锁
    bool connectState = false;        ///< 连接状态
    static const int MAX_LOG_BUFFER = 1024;        ///< 最大日志缓冲区大小
    static const int LOG_UPDATE_INTERVAL = 200;    ///< 日志更新间隔（毫秒）
};

#endif // QINGBAO_H
