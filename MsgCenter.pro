#-------------------------------------------------
#
# Project created by QtCreator 2025-04-09T13:39:48
#
#-------------------------------------------------
QT += core gui network websockets widgets concurrent serialport

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = MsgCenter
TEMPLATE = app

# The following define makes your compiler emit warnings if you use
# any feature of Qt which has been marked as deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

CONFIG += c++17

# FFmpeg 配置
win32: {
    # Windows上FFmpeg库路径，可以使用绝对路径或相对路径
    FFMPEG_HOME = $$PWD/../thirdpart/ffmpeg # 替换为实际安装路径

    INCLUDEPATH += $$FFMPEG_HOME/include
    
    LIBS += -L$$FFMPEG_HOME/lib \
            -lavcodec \
            -lavformat \
            -lavutil \
            -lswscale

    LIBS += -L$$FFMPEG_HOME/bin
}

unix: {
    # Linux上使用系统安装的FFmpeg
    CONFIG += link_pkgconfig
    PKGCONFIG += libavcodec libavformat libavutil libswscale
}

SOURCES += \
        DaoTiao.cpp \
        JustifyPosition.cpp \
        MessageWorker.cpp \
        QingBao.cpp \
        QingBaoWorker.cpp \
        YouRenJi.cpp \
        YouRenJiWorker.cpp \
        main.cpp \
        Widget.cpp

HEADERS += \
        DaoTiao.h \
        JustifyPosition.h \
        MessageWorker.h \
        QingBao.h \
        QingBaoWorker.h \
        Widget.h \
        YouRenJi.h \
        YouRenJiWorker.h \
        protocol.h

FORMS += \
        DaoTiao.ui \
        QingBao.ui \
        Widget.ui \
        YouRenJi.ui
# 解决utf-8乱码
msvc:QMAKE_CXXFLAGS += -execution-charset:utf-8
msvc:QMAKE_CXXFLAGS += -source-charset:utf-8
#QMAKE_CXXFLAGS_WARN_ON += -wd4819
# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
