﻿#ifndef DATAPOOL_H
#define DATAPOOL_H

#include <QQueue>
#include <QMutex>
#include <QWaitCondition>

template<typename T>
class DataPool {
public:
    void put(const T &data) {
        QMutexLocker locker(&m_mutex);
        m_queue.enqueue(data);
        m_condition.wakeOne();
    }

    T get() {
        QMutexLocker locker(&m_mutex);
        while (m_queue.isEmpty()) {
            m_condition.wait(&m_mutex);
        }
        return m_queue.dequeue();
    }

private:
    QQueue<T> m_queue;
    QMutex m_mutex;
    QWaitCondition m_condition;
};

#endif // DATAPOOL_H
