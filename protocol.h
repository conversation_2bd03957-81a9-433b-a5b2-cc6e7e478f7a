﻿#ifndef PROTOCOL_H
#define PROTOCOL_H

#include <vector>
#include "stdint.h"

#pragma pack(push,1)
typedef struct MDS_HEADER
{
    uint16_t message_code_uh;
    uint16_t message_length_uh;
    uint64_t timestamp_ul;//发送消息时间戳
    uint16_t message_number_uh;
    uint8_t message_priority_e;
    uint8_t spare;
}MDS_HEADER;

typedef struct CVPOINT2D32F {
    float point_x_f;
    float point_y_f;
}CVPOINT2D32F;

typedef struct CORRECTION_PARAM {
    float left_longitude_f;
    float right_longitude_f;
    float top_latitude_f;
    float bottom_latitude_f;
    CVPOINT2D32F dst_point1;
    CVPOINT2D32F dst_point2;
    CVPOINT2D32F dst_point3;
    CVPOINT2D32F dst_point4;
    CVPOINT2D32F src_point1;
    CVPOINT2D32F src_point2;
    CVPOINT2D32F src_point3;
    CVPOINT2D32F src_point4;
}CORRECTION_PARAM;

typedef struct LOCAL_DATA_FORMAT {
    MDS_HEADER msgh;
    uint16_t plat_id_uh;
    uint32_t photo_number_ul;
    uint32_t time_of_update_ul;
    uint16_t total_ackage_number_uh;
    uint16_t package_number_uh;
    uint32_t package_length_ul;
    CORRECTION_PARAM correction_param;
    std::vector<unsigned char> data;
}LOCAL_DATA_FORMAT;



struct JW
{
    double J;//经度
    double W;
};

struct JWInfo
{
    struct JW center;
    struct JW pointLB;
    struct JW pointRB;
    struct JW pointRU;
    struct JW pointLU;
};

struct INPUT_TEST {
    int nImageType;       //图像类型 0可见光 1红外 2 数码照片
    double fLongitude;    //飞机经度——范围：-180到180
    double fLatitude;     //飞机纬度——范  围：-180到180
    double fFlyHeight;    //飞行高度——范围：大于0
    double fPlanePitch;   //飞机俯仰角——范围：0-90
    double fPlaneRoll;    //飞机滚转角——范围：-50到50
    double fPlaneYaw;     //飞机航向角——范围：0-360
    double fCameraPitch;  //相机俯仰角或叫高低角度——范围：-90到90
    double fCameraYaw;    //相机方位角——范围：0-360
    double fCameraFocus;  //相机焦距————范围：大于0
    double fTerrainHeight;    //地形高度 默认填0
};

struct OUTPUT_TEST {
    double leftLongitude;       //校正后图像左经度，单位：度（°）
    double rightLongitude;         //校正后图像右纬度，单位：度（°）
    double topLatitude;          //校正后图像上纬度，单位：度（°）
    double bottomLatitude;             //校正后图像下纬度，单位：度（°）
    double m1;            //透视变换矩阵参数1，
    double m2;            //透视变换矩阵参数2，
    double m3;            //透视变换矩阵参数3，
    double m4;            //透视变换矩阵参数4，
    double m5;            //透视变换矩阵参数5，
    double m6;            //透视变换矩阵参数6，
    double m7;            //透视变换矩阵参数7，
    double m8;            //透视变换矩阵参数8，
};
#pragma pack(pop)
#endif // PROTOCOL_H
