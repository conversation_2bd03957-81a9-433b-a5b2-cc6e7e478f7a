﻿#include "YouRenJi.h"
#include "ui_YouRenJi.h"
#include <QDebug>
#include <QRegExp>
#include <QRegExpValidator>
#include <QScrollBar>
#include <QSpinBox>
#include <QLabel>
#include <QVBoxLayout>
#include <QTimer>
#include <QMutex>
#include <QTextCursor>
#include <QDateTime>
#include <QFontDatabase>
#include <QMetaType>
#include <QSerialPortInfo>
#include <QMessageBox>
#include <QApplication>
#include <QStyle>

YouRenJi::YouRenJi(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::YouRenJi),
    connectState(false),
    serialPortOpen(false),
    worker(new YouRenJiWorker),
    logUpdateTimer(nullptr),
    comRefreshTimer(nullptr),
    serialPort(nullptr)
{
    // 注册元类型，以支持跨线程信号槽
    qRegisterMetaType<QAbstractSocket::SocketError>("QAbstractSocket::SocketError");
    qRegisterMetaType<QAbstractSocket::SocketState>("QAbstractSocket::SocketState");
    qRegisterMetaType<QSerialPort::SerialPortError>("QSerialPort::SerialPortError");

    ui->setupUi(this);
    
    // 设置窗口图标和标题
    setWindowIcon(QApplication::style()->standardIcon(QStyle::SP_ComputerIcon));

    // 初始化UI组件
    initUiComponents();

    // 初始化串口设置
    initSerialPortSettings();

    // 不要立即启动工作线程，等到需要时再启动
    worker->moveToThread(&workerThread);

    // 连接信号和槽
    connect(&workerThread, &QThread::finished, worker, &QObject::deleteLater);
    connect(worker, &YouRenJiWorker::messageReceived, this, &YouRenJi::handleMessage, Qt::QueuedConnection);
    connect(worker, &YouRenJiWorker::errorOccurred, this, &YouRenJi::handleError, Qt::QueuedConnection);
    connect(worker, &YouRenJiWorker::connectionStatusChanged, this, &YouRenJi::updateConnectionStatus, Qt::QueuedConnection);
    connect(worker, &YouRenJiWorker::serialPortStatusChanged, this, [this](bool opened) {
        serialPortOpen = opened;
        ui->COMState->setText(opened ? "已打开" : "已关闭");
        ui->COMState->setStyleSheet(opened ? "color: green; font-weight: bold;" : "color: red; font-weight: bold;");
        ui->COMClick->setText(opened ? "关闭串口" : "打开串口");
    }, Qt::QueuedConnection);
    connect(worker, &YouRenJiWorker::serialErrorOccurred, this, &YouRenJi::handleSerialError, Qt::QueuedConnection);
    connect(ui->yourenji, &QPushButton::clicked, this, &YouRenJi::onConnect);
    connect(ui->COMClick, &QPushButton::clicked, this, &YouRenJi::onComPortToggle);
    
    // 连接菜单项
    connect(ui->actionExit, &QAction::triggered, this, &QApplication::quit);
    connect(ui->actionClearLogs, &QAction::triggered, this, [this]() {
        ui->yourenjiMsg->clear();
        logBuffer.clear();
        handleMessage(QString("[%1] <span class='system'>[系统] 日志已清除</span>")
                     .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
    });
    connect(ui->actionRefreshPorts, &QAction::triggered, this, &YouRenJi::refreshComPorts);
    connect(ui->actionAbout, &QAction::triggered, this, [this]() {
        QMessageBox::about(this, "关于串口到载荷组播转发器", 
            "<h3>串口到载荷组播转发器 v1.0</h3>"
            "<p>一个用于串口和网络数据转发的应用程序</p>"
            "<p>实现无人机组播数据与串口设备之间的转换与传输</p>");
    });
    
    // 连接采样率控件
    connect(ui->msgSampleSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
        [this](int value) { worker->setMessageSamplingRate(value); });
    connect(ui->logSampleSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
        [this](int value) { worker->setLogSamplingRate(value); });
    
    // 连接性能监控信号
    connect(worker, &YouRenJiWorker::throughputUpdated, this, [this](int messagesPerSecond, int dataBytes) {
        ui->throughputLabel->setText(QString("<b>消息吞吐量:</b> %1 消息/秒").arg(messagesPerSecond));
        ui->bytesLabel->setText(QString("<b>数据处理量:</b> %1 字节/秒").arg(dataBytes));
        
        // 更新状态栏
        statusBar()->showMessage(QString("消息: %1/秒 | 数据: %2 字节/秒").arg(messagesPerSecond).arg(dataBytes));
    });

    // 显示初始状态信息
    handleMessage(QString("[%1] <span class='system'>[系统] 程序已启动，等待连接...</span>")
                 .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
}

YouRenJi::~YouRenJi()
{
    // 先停止工作线程中的UDP任务
    if (worker) {
        QMetaObject::invokeMethod(worker, &YouRenJiWorker::stop, Qt::QueuedConnection);

        // 如果串口是开着的，显式关闭它
        if (serialPortOpen) {
            QMetaObject::invokeMethod(worker, &YouRenJiWorker::stopSerialPort, Qt::QueuedConnection);
        }
    }

    // 安全停止工作线程
    const int TIMEOUT_MS = 3000; // 3秒超时
    workerThread.quit();
    if (!workerThread.wait(TIMEOUT_MS)) {
        qWarning("YouRenJi worker thread failed to quit in %d ms, forcing termination", TIMEOUT_MS);
        workerThread.terminate();
        workerThread.wait();
    }

    // 停止定时器
    if (logUpdateTimer) {
        logUpdateTimer->stop();
        delete logUpdateTimer;
        logUpdateTimer = nullptr;
    }

    if (comRefreshTimer) {
        comRefreshTimer->stop();
        delete comRefreshTimer;
        comRefreshTimer = nullptr;
    }

    delete ui;
}

void YouRenJi::initUiComponents()
{
    // 设置日志更新定时器
    logUpdateTimer = new QTimer(this);
    logUpdateTimer->setInterval(LOG_UPDATE_INTERVAL);
    connect(logUpdateTimer, &QTimer::timeout, this, &YouRenJi::updateLogDisplay);
    logUpdateTimer->start();

    // 设置文本浏览器属性为启用富文本
    ui->yourenjiMsg->setAcceptRichText(true);
    ui->yourenjiMsg->document()->setMaximumBlockCount(10000); // 限制最大行数，防止内存溢出

    // 设置等宽字体，使日志显示更整齐
    QFont logFont = QFontDatabase::systemFont(QFontDatabase::FixedFont);
    logFont.setPointSize(9);
    ui->yourenjiMsg->setFont(logFont);

    // 设置CSS样式，美化日志显示
    ui->yourenjiMsg->document()->setDefaultStyleSheet(
        "body { color: #333; }"
        ".system { color: #008800; }"
        ".error { color: #cc0000; font-weight: bold; }"
        ".data { color: #0000aa; }"
        ".timestamp { color: #888; font-weight: normal; }"
        ".highlight { background-color: #ffffcc; }"
        ".video { color: #9900cc; }"
        ".serial { color: #ff6600; }"
    );

    // 创建正则表达式验证器
    QRegExp ipPortRegExp(R"((\d{1,3}\.){3}\d{1,3}:\d{1,5})");
    QRegExpValidator* validator = new QRegExpValidator(ipPortRegExp);

    // 将验证器应用到输入框
    ui->zaiheSend->setValidator(validator);
    ui->zaiheSend->setText("*********:6604");

    ui->uavReceive->setValidator(validator);
    ui->uavReceive->setText("***********:7002");
}

void YouRenJi::initSerialPortSettings()
{
    // 创建串口刷新定时器
    comRefreshTimer = new QTimer(this);
    comRefreshTimer->setInterval(2000); // 每2秒刷新一次可用串口
    connect(comRefreshTimer, &QTimer::timeout, this, &YouRenJi::refreshComPorts);
    comRefreshTimer->start();

    // 初始波特率选项
    ui->BaudRate->addItem("9600", QSerialPort::Baud9600);
    ui->BaudRate->addItem("19200", QSerialPort::Baud19200);
    ui->BaudRate->addItem("38400", QSerialPort::Baud38400);
    ui->BaudRate->addItem("57600", QSerialPort::Baud57600);
    ui->BaudRate->addItem("115200", QSerialPort::Baud115200);
    ui->BaudRate->setCurrentIndex(4); // 默认选择115200

    // 初始数据位选项
    ui->DataBits->addItem("5", QSerialPort::Data5);
    ui->DataBits->addItem("6", QSerialPort::Data6);
    ui->DataBits->addItem("7", QSerialPort::Data7);
    ui->DataBits->addItem("8", QSerialPort::Data8);
    ui->DataBits->setCurrentIndex(3); // 默认选择8位

    // 初始校验位选项
    ui->ParityBits->addItem("无", QSerialPort::NoParity);
    ui->ParityBits->addItem("奇", QSerialPort::OddParity);
    ui->ParityBits->addItem("偶", QSerialPort::EvenParity);
    ui->ParityBits->addItem("标志", QSerialPort::MarkParity);
    ui->ParityBits->addItem("空格", QSerialPort::SpaceParity);
    ui->ParityBits->setCurrentIndex(0); // 默认选择无校验

    // 初始停止位选项
    ui->StopBits->addItem("1", QSerialPort::OneStop);
    ui->StopBits->addItem("1.5", QSerialPort::OneAndHalfStop);
    ui->StopBits->addItem("2", QSerialPort::TwoStop);
    ui->StopBits->setCurrentIndex(0); // 默认选择1位停止位

    // 立即刷新可用串口列表
    refreshComPorts();
}

void YouRenJi::refreshComPorts()
{
    // 保存当前选择的串口
    QString currentPort = ui->ComPort->currentText();

    // 清空串口列表
    ui->ComPort->clear();

    // 获取可用串口列表
    const auto serialPortInfos = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &info : serialPortInfos) {
        QString portName = info.portName();
        QString description = info.description();
        ui->ComPort->addItem(portName + " - " + description, portName);
    }

    // 如果之前有选择的串口，尝试恢复选择
    if (!currentPort.isEmpty()) {
        int index = ui->ComPort->findText(currentPort, Qt::MatchStartsWith);
        if (index >= 0) {
            ui->ComPort->setCurrentIndex(index);
        }
    }

    // 如果没有可用串口，禁用打开串口按钮
    ui->COMClick->setEnabled(ui->ComPort->count() > 0);
    
    // 更新状态栏
    if (ui->ComPort->count() > 0) {
        statusBar()->showMessage("已检测到 " + QString::number(ui->ComPort->count()) + " 个串口设备");
    } else {
        statusBar()->showMessage("未检测到串口设备");
    }
}

void YouRenJi::onComPortToggle()
{
    if (serialPortOpen) {
        // 关闭串口
        QMetaObject::invokeMethod(worker, &YouRenJiWorker::stopSerialPort, Qt::QueuedConnection);
    } else {
        // 确保工作线程已经启动，但不影响UDP连接状态
        if (!workerThread.isRunning()) {
            workerThread.start();
        }
        // 配置并打开串口
        configureSerialPort();
    }
}

void YouRenJi::configureSerialPort()
{
    if (ui->ComPort->count() == 0) return;

    QString portName = ui->ComPort->currentData().toString();
    int baudRate = ui->BaudRate->currentData().toInt();
    QSerialPort::DataBits dataBits = static_cast<QSerialPort::DataBits>(ui->DataBits->currentData().toInt());
    QSerialPort::Parity parity = static_cast<QSerialPort::Parity>(ui->ParityBits->currentData().toInt());
    QSerialPort::StopBits stopBits = static_cast<QSerialPort::StopBits>(ui->StopBits->currentData().toInt());

    // 使用QMetaObject::invokeMethod确保在worker线程中执行方法
    QMetaObject::invokeMethod(worker, [this, portName, baudRate, dataBits, parity, stopBits]() {
        worker->setSerialPortSettings(portName, baudRate, dataBits, parity, stopBits);
        worker->startSerialPort();
    }, Qt::QueuedConnection);
}

void YouRenJi::onConnect()
{
    if(connectState) {
        // 使用QMetaObject::invokeMethod确保在worker线程中执行方法
        QMetaObject::invokeMethod(worker, &YouRenJiWorker::stop, Qt::QueuedConnection);

        ui->yourenji->setText("启动转发");
        handleMessage(QString("[%1] <span class='system'>[系统] 已停止数据转发</span>")
                     .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
    } else {
        // 确保工作线程已经启动
        if (!workerThread.isRunning()) {
            workerThread.start();
        }

        // 获取载荷组播地址
        QStringList tmp = ui->zaiheSend->text().split(":");
        QString zaiheSendIP = tmp[0];
        QString zaiheSendPort = tmp[1];

        // 获取无人机组播地址
        tmp = ui->uavReceive->text().split(":");
        QString uavRecvIP = tmp[0];
        QString uavRecvPort = tmp[1];

        // 配置并启动工作线程，使用QMetaObject::invokeMethod确保在worker线程中执行方法
        QMetaObject::invokeMethod(worker, [this, zaiheSendIP, zaiheSendPort,
                                         uavRecvIP, uavRecvPort]() {
            worker->setAddresses(zaiheSendIP, zaiheSendPort,
                               uavRecvIP, uavRecvPort);
            worker->start();
        }, Qt::QueuedConnection);

        ui->yourenji->setText("停止转发");
        handleMessage(QString("[%1] <span class='system'>[系统] 正在启动数据转发...</span>")
                     .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
    }
    connectState = !connectState;
}

void YouRenJi::handleMessage(const QString &message)
{
    QMutexLocker locker(&logMutex);
    if (logBuffer.size() >= MAX_LOG_BUFFER) {
        logBuffer.dequeue();
    }

    // 消息已经包含时间戳，由YouRenJiWorker添加
    logBuffer.enqueue(message);
}

void YouRenJi::handleError(const QString &error)
{
    QMutexLocker locker(&logMutex);
    if (logBuffer.size() >= MAX_LOG_BUFFER) {
        logBuffer.dequeue();
    }

    // 错误消息已经包含时间戳，由YouRenJiWorker添加
    logBuffer.enqueue("<span class='error'>" + error + "</span>");
}

void YouRenJi::handleSerialError(const QString &error)
{
    QMutexLocker locker(&logMutex);
    if (logBuffer.size() >= MAX_LOG_BUFFER) {
        logBuffer.dequeue();
    }

    // 串口错误消息
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    logBuffer.enqueue(QString("[%1] <span class='error'>[串口错误] %2</span>").arg(timestamp).arg(error));
}

void YouRenJi::updateConnectionStatus(bool connected)
{
    QString style = connected ? "background-color: lime; border-radius: 8px;"
                              : "background-color: red; border-radius: 8px;";
    ui->led->setStyleSheet(style);

    // 添加连接状态变更日志
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    if (connected) {
        handleMessage(QString("[%1] <span class='system'>[系统] 数据转发已启动</span>").arg(timestamp));
    } else {
        handleMessage(QString("[%1] <span class='system'>[系统] 数据转发已停止</span>").arg(timestamp));
    }
}

void YouRenJi::updateLogDisplay()
{
    if(worker->logCounter > 500){
        worker->logCounter = 0;
        logBuffer.clear();
        ui->yourenjiMsg->clear();
    }
    QMutexLocker locker(&logMutex);
    if (logBuffer.isEmpty()) return;

    // 限制每次更新的消息数量，避免UI卡顿
    const int MAX_MESSAGES_PER_UPDATE = 50;
    int messagesToProcess = qMin(logBuffer.size(), MAX_MESSAGES_PER_UPDATE);
    
    QString allLogs;
    for (int i = 0; i < messagesToProcess; i++) {
        allLogs += logBuffer.dequeue() + "<br>";
    }

    // 获取当前滚动位置
    QScrollBar* scrollBar = ui->yourenjiMsg->verticalScrollBar();
    bool atBottom = scrollBar->value() >= scrollBar->maximum() - 10;

    // 添加新日志
    ui->yourenjiMsg->moveCursor(QTextCursor::End);
    ui->yourenjiMsg->insertHtml(allLogs);

    // 如果之前在底部，自动滚动到底部
    if (atBottom) {
        scrollBar->setValue(scrollBar->maximum());
    }
}
