﻿#include "JustifyPosition.h"
#include "math.h"
struct JW JustifyPosition::BLToXY(double latitude, double longitude)
{
    struct JW valueReturn;
    double m0, m2, m4, m6, m8;	//牛顿二项式展开级数系数
    double a0, a2, a4, a6, a8;			//延经线弧长公式系数
    double a;			//椭球长轴
    double e1_2;		//第一偏心率e1的平方
    double e2_2;		//第二偏心率e2的平方
    double L0;			//中央子午线经度
    double Xs, Ys;       //转换后的X,Y坐标
    // 84坐标系参数
    a = 6378137;						//椭球长半轴
    e1_2 = 0.0066943799013;			//第一偏心率平方
    e2_2 = 0.00673949674227;			//第二偏心率平方
    m0 = a * (1 - e1_2);
    m2 = 3 * m0 * e1_2 / 2;
    m4 = 5 * m2 * e1_2 / 4;
    m6 = 7 * m4 * e1_2 / 6;
    m8 = 9 * m6 * e1_2 / 8;
    a0 = m0 + m2 / 2 + 3 * m4 / 8 + 5 * m6 / 16 + 35 * m8 / 128;
    a2 = m2 / 2 + m4 / 2 + 15 * m6 / 32 + 7 * m8 / 16;
    a4 = m4 / 8 + 3 * m6 / 16 + 7 * m8 / 32;
    a6 = m6 / 32 + m8 / 16;
    a8 = m8 / 128;
    double B = latitude;
    double L = longitude;
    int zone;
    if (fmod(L, 6) != 0)				//求带号
    {
        zone = (int)(L / 6) + 1;
    }
    else
        zone = (int)(L / 6);
    L0 = 6 * zone - 3;		//求中央经线
    double l;
    l = L - L0;                   //求经差
    l = l / 180 * pi;			    //经差用弧度表示
    B = B / 180 * pi;				//弧度表示
    L = L / 180 * pi;				//弧度表示
    double t, eta_2, N;			//t和η的平方
    t = tan(B);
    eta_2 = e2_2 * cos(B) * cos(B);
    N = a / sqrt(1 - e1_2 * sin(B) * sin(B));		//计算卯酉圈半径
    double 	k = a0 * B - a2 * sin(2 * B) / 2 + a4 * sin(4 * B) / 4 - a6 * sin(6 * B) / 6 + a8 * sin(8 * B) / 8;
    double cosbl_2, cosbl_3, cosbl_4, cosbl_5, cosbl_6;
    cosbl_2 = cos(B) * cos(B) * l * l;
    cosbl_3 = cos(B) * cos(B) * cos(B) * l * l * l;
    cosbl_4 = cos(B) * cos(B) * cos(B) * cos(B) * l * l * l * l;
    cosbl_5 = cos(B) * cos(B) * cos(B) * cos(B) * cos(B) * l * l * l * l * l;
    cosbl_6 = cos(B) * cos(B) * cos(B) * cos(B) * cos(B) * cos(B) * l * l * l * l * l * l;
    Xs = k + N * t * cosbl_2 / 2 + N * t * (5 - t * t + 9 * eta_2 + 4 * eta_2 * eta_2) * cosbl_4 / 24 + N * t * (61 - 58 * t * t + t * t * t * t) * cosbl_6 / 720;
    Ys = N * l * cos(B) + N * (1 - t * t + eta_2) * cosbl_3 / 6 + N * (5 - 18 * t * t + t * t * t * t + 14 * eta_2 - 58 * t * t * eta_2) * cosbl_5 / 120;
    Ys += 500000;			//y坐标要加上500000m
    Ys += zone * 1000000;	    //y坐标加上带号
    valueReturn.J = Xs;
    valueReturn.W = Ys;

    return valueReturn;
}
struct JW JustifyPosition::XYToBL(double X, double Y)
{
    struct JW  jw;
    double Latitude, Longitude;
    double a = 6378137;			//椭球长轴
    double e1_2;		//第一偏心率e1的平方
    double e2_2;		//第二偏心率e2的平方
    e1_2 = 0.0066943799013;			//第一偏心率平方
    e2_2 = 0.00673949674227;			//第二偏心率平方
    int zone;
    double y = Y;
    zone = (int)(y / 1000000);  //求带号
    double L0 = zone * 6 - 3;			//中央子午线经度
    y = y - zone * 1000000 - 500000;		//y的真实坐标
    double Bf;
    double x = X;
    double b0, c0, e1_4, e1_6, e1_8, e1_10, e1_12, e1_14, e1_16;
    e1_4 = pow(e1_2, 2);
    e1_6 = pow(e1_2, 3);
    e1_8 = pow(e1_2, 4);
    e1_10 = pow(e1_2, 5);
    e1_12 = pow(e1_2, 6);
    e1_14 = pow(e1_2, 7);
    e1_16 = pow(e1_2, 8);
    c0 = 1 + e1_2 / 4 + 7 * e1_4 / 64 + 15 * e1_6 / 256 + 579 * e1_8 / 16384 + 1515 * e1_10 / 65536 + 16837 * e1_12 / 1048576 + 48997 * e1_14 / 4194304 + 9467419 * e1_16 / 1073741824;
    c0 = a / c0;
    //	m=3380330.875;
    b0 = x / c0;
    double d1, d2, d3, d4, d5, d6, d7;
    d1 = 3 * e1_2 / 8 + 45 * e1_4 / 128 + 175 * e1_6 / 512 + 11025 * e1_8 / 32768 + 43659 * e1_10 / 131072 + 693693 * e1_12 / 2097152 + 10863435 * e1_14 / 33554432;
    d2 = -21 * e1_4 / 64 - 277 * e1_6 / 384 - 19413 * e1_8 / 16384 - 56331 * e1_10 / 32768 - 2436477 * e1_12 / 1048576 - 196473 * e1_14 / 65536;
    d3 = 151 * e1_6 / 384 + 5707 * e1_8 / 4096 + 53189 * e1_10 / 163840 + 4599609 * e1_12 / 655360 + 15842375 * e1_14 / 1048576;
    d4 = -1097 * e1_8 / 2048 - 1687 * e1_10 / 640 - 3650333 * e1_12 / 327680 - 114459079 * e1_14 / 27525120;
    d5 = 8011 * e1_10 / 1024 + 874457 * e1_12 / 98304 + 216344925 * e1_14 / 3670016;
    d6 = -682193 * e1_12 / 245760 - 46492223 * e1_14 / 1146880;
    d7 = 36941521 * e1_14 / 3440640;
    Bf = b0 + sin(2 * b0) * (d1 + sin(b0) * sin(b0) * (d2 + sin(b0) * sin(b0) * (d3 + sin(b0) * sin(b0) * (d4 + sin(b0) * sin(b0) * (d5 + sin(b0) * sin(b0) * (d6 + d7 * sin(b0) * sin(b0)))))));
    double Mf, Nf, tf, etaf_2;
    Mf = (a * (1 - e1_2)) / sqrt((1 - e1_2 * sin(Bf) * sin(Bf)) * (1 - e1_2 * sin(Bf) * sin(Bf)) * (1 - e1_2 * sin(Bf) * sin(Bf)));
    Nf = a / sqrt(1 - e1_2 * sin(Bf) * sin(Bf));
    tf = tan(Bf);
    etaf_2 = e2_2 * cos(Bf) * cos(Bf);
    double tf_2, tf_4, y_2, y_3, y_4, y_5, y_6, Nf_3, Nf_5;
    tf_2 = tf * tf;
    tf_4 = tf_2 * tf_2;
    y_2 = y * y;
    y_3 = y_2 * y;
    y_4 = y_3 * y;
    y_5 = y_4 * y;
    y_6 = y_5 * y;
    Nf_3 = pow(Nf, 3);
    Nf_5 = pow(Nf, 5);
    double B, L;
    B = Bf - tf * y_2 / (2 * Mf * Nf) + tf * (5 + 3 * tf_2 + etaf_2 - 9 * etaf_2 * tf_2) * y_4 / (24 * Mf * Nf_3) - tf * (61 + 90 * tf_2 + 45 * tf_4) * y_6 / (720 * Mf * Nf_5);
    L = y / (Nf * cos(Bf)) - y_3 * (1 + 2 * tf_2 + etaf_2) / (6 * Nf_3 * cos(Bf)) + (5 + 28 * tf_2 + 24 * tf_4 + 6 * etaf_2 + 8 * etaf_2 * tf_2) * y_5 / (120 * Nf_5 * cos(Bf));
    Longitude = L0 + L * 180 / pi;
    Latitude = B * 180 / pi;
    jw.J = Longitude;
    jw.W = Latitude;
    return jw;
}

double JustifyPosition::GetDoubleMaxValue(double* array, int count)
{
    double MaxValue = array[0];
    for (int i = 0; i < count; i++)
    {
        if (array[i] > MaxValue)
        {
            MaxValue = array[i];
        }
    }
    return MaxValue;
}

double JustifyPosition::GetDoubleMinValue(double* array, int count)
{
    double MinValue = array[0];
    for (int i = 0; i < count; i++)
    {
        if (array[i] < MinValue)
        {
            MinValue = array[i];
        }
    }
    return MinValue;
}
/******************************InitParamBX**********************************/
int JustifyPosition::InitParamBX(int inImageType, double fLongitude, double fLatitude, double fFlyHeight, double fTerrainHeight, double fPlanePitch, double fPlaneRoll, double fPlaneYaw, double fCameraPitch, double fCameraYaw, double fCameraFocus, double fCameraPixelSize, int imgW, int imgH)
{
    m_fLongitude = fLongitude;
    m_fLatitude = fLatitude;
    m_fFlyHeight = fFlyHeight;
    m_fTerrainHeight = fTerrainHeight;
    m_fPlanePitch = fPlanePitch;
    m_fPlaneRoll = fPlaneRoll;
    m_fPlaneYaw = fPlaneYaw;
    m_fCameraPitch = fCameraPitch;
    m_fCameraYaw = fCameraYaw;
    m_fCameraFocus = fCameraFocus * 0.001;
    m_fCameraPixelSize = fCameraPixelSize;
    //m_imgW = imgW;
    //m_imgH = imgH;
    if (inImageType == 0) {//可见光
        m_fCameraPixelSize = 0.000006;
        m_imgW = 1920;
        m_imgH = 1080;
        return 0;
    }

    if (inImageType == 2) {//可见光
        m_fCameraPixelSize = 0.0000055;
        m_imgW = 8984;
        m_imgH = 6732;
        return 0;
    }

    if (inImageType == 1) {//红外
        m_fCameraPixelSize = 0.000015;
        m_imgW = 640;
        m_imgH = 512;
        return 0;
    }

    return -1;
}
/******************************GeoCorrectBX**********************************/
struct JWInfo JustifyPosition::GeoCorrectBX(CVPOINT2D32F(*srcpoint_out)[4], CVPOINT2D32F(*dstpoint_out)[4])
{
    struct JWInfo jwInfo;
    double pitch, roll, yaw;//飞机姿态
    double Height;//飞机位置
    double PTFW, PTFY;//相机姿态
    double Xs, Ys;//飞机位置大地坐标
    double x1, y1, z1, x2, y2, z2, x3, y3, z3, x4, y4, z4, X1, Y1, X2, Y2, X3, Y3, X4, Y4, u, f, x5, y5, z5;
    double x11, y11, z11, x22, y22, z22, x33, y33, z33, x44, y44, z44, x55, y55, z55;

    int ImgWidth, ImgHeight;//图像尺寸

    /******************************初始参数赋值**********************************/
    pitch = -(m_fPlanePitch) / 180.0 * pi;
    roll = -(m_fPlaneRoll) / 180.0 * pi;
    yaw = -(m_fPlaneYaw) / 180.0 * pi;
    PTFW = -(m_fCameraYaw) / 180.0 * pi;
    PTFY = -(90 + m_fCameraPitch) / 180.0 * pi;
    Height = (m_fFlyHeight - m_fTerrainHeight);

    struct JW XY;
    XY = BLToXY(m_fLatitude, m_fLongitude);
    Xs = XY.J;// - 1513;
    Ys = XY.W;// + 25;
    u = m_fCameraPixelSize;
    f = m_fCameraFocus;

    double x_offset, y_offset;
    x_offset = (f / u) * tan(roll);
    y_offset = (f / u) * tan(pitch);

    ImgWidth = m_imgW;
    ImgHeight = m_imgH;

    /***********************像平面四个角点赋值***************/
    x1 = -ImgHeight / 2 * u;   y1 = -ImgWidth / 2 * u;    z1 = -f;
    x2 = -ImgHeight / 2 * u;   y2 = ImgWidth / 2 * u;    z2 = -f;
    x3 = ImgHeight / 2 * u;    y3 = ImgWidth / 2 * u;   z3 = -f;
    x4 = ImgHeight / 2 * u;    y4 = -ImgWidth / 2 * u;    z4 = -f;
    x5 = 0;                 y5 = 0;                 z5 = -f;

    x11 = cos(PTFY) * x1 - sin(PTFY) * z1;
    y11 = y1;
    z11 = sin(PTFY) * x1 + cos(PTFY) * z1;

    x22 = cos(PTFY) * x2 - sin(PTFY) * z2;
    y22 = y2;
    z22 = sin(PTFY) * x2 + cos(PTFY) * z2;

    x33 = cos(PTFY) * x3 - sin(PTFY) * z3;
    y33 = y3;
    z33 = sin(PTFY) * x3 + cos(PTFY) * z3;

    x44 = cos(PTFY) * x4 - sin(PTFY) * z4;
    y44 = y4;
    z44 = sin(PTFY) * x4 + cos(PTFY) * z4;

    x55 = cos(PTFY) * x5 - sin(PTFY) * z5;
    y55 = y5;
    z55 = sin(PTFY) * x5 + cos(PTFY) * z5;

    x1 = x11;   y1 = y11;  z1 = z11;
    x2 = x22;   y2 = y22;  z2 = z22;
    x3 = x33;   y3 = y33;  z3 = z33;
    x4 = x44;   y4 = y44;  z4 = z44;
    x5 = x55;   y5 = y55;  z5 = z55;



    x11 = cos(PTFW) * x1 - sin(PTFW) * y1;
    y11 = sin(PTFW) * x1 + cos(PTFW) * y1;
    z11 = z1;

    x22 = cos(PTFW) * x2 - sin(PTFW) * y2;
    y22 = sin(PTFW) * x2 + cos(PTFW) * y2;
    z22 = z2;

    x33 = cos(PTFW) * x3 - sin(PTFW) * y3;
    y33 = sin(PTFW) * x3 + cos(PTFW) * y3;
    z33 = z3;

    x44 = cos(PTFW) * x4 - sin(PTFW) * y4;
    y44 = sin(PTFW) * x4 + cos(PTFW) * y4;
    z44 = z4;

    x55 = cos(PTFW) * x5 - sin(PTFW) * y5;
    y55 = sin(PTFW) * x5 + cos(PTFW) * y5;
    z55 = z5;

    x1 = x11;   y1 = y11;  z1 = z11;
    x2 = x22;   y2 = y22;  z2 = z22;
    x3 = x33;   y3 = y33;  z3 = z33;
    x4 = x44;   y4 = y44;  z4 = z44;
    x5 = x55;   y5 = y55;  z5 = z55;


    x11 = cos(pitch) * x1 - sin(pitch) * z1;
    y11 = y1;
    z11 = sin(pitch) * x1 + cos(pitch) * z1;

    x22 = cos(pitch) * x2 - sin(pitch) * z2;
    y22 = y2;
    z22 = sin(pitch) * x2 + cos(pitch) * z2;

    x33 = cos(pitch) * x3 - sin(pitch) * z3;
    y33 = y3;
    z33 = sin(pitch) * x3 + cos(pitch) * z3;

    x44 = cos(pitch) * x4 - sin(pitch) * z4;
    y44 = y4;
    z44 = sin(pitch) * x4 + cos(pitch) * z4;

    x55 = cos(pitch) * x5 - sin(pitch) * z5;
    y55 = y5;
    z55 = sin(pitch) * x5 + cos(pitch) * z5;

    x1 = x11;   y1 = y11;  z1 = z11;
    x2 = x22;   y2 = y22;  z2 = z22;
    x3 = x33;   y3 = y33;  z3 = z33;
    x4 = x44;   y4 = y44;  z4 = z44;
    x5 = x55;   y5 = y55;  z5 = z55;

    x11 = x1;
    y11 = cos(roll) * y1 - sin(roll) * z1;
    z11 = sin(roll) * y1 + cos(roll) * z1;

    x22 = x2;
    y22 = cos(roll) * y2 - sin(roll) * z2;
    z22 = sin(roll) * y2 + cos(roll) * z2;

    x33 = x3;
    y33 = cos(roll) * y3 - sin(roll) * z3;
    z33 = sin(roll) * y3 + cos(roll) * z3;

    x44 = x4;
    y44 = cos(roll) * y4 - sin(roll) * z4;
    z44 = sin(roll) * y4 + cos(roll) * z4;

    x55 = x5;
    y55 = cos(roll) * y5 - sin(roll) * z5;
    z55 = sin(roll) * y5 + cos(roll) * z5;

    x1 = x11;   y1 = y11;  z1 = z11;
    x2 = x22;   y2 = y22;  z2 = z22;
    x3 = x33;   y3 = y33;  z3 = z33;
    x4 = x44;   y4 = y44;  z4 = z44;
    x5 = x55;   y5 = y55;  z5 = z55;

    x11 = cos(yaw) * x1 - sin(yaw) * y1;
    y11 = sin(yaw) * x1 + cos(yaw) * y1;
    z11 = z1;

    x22 = cos(yaw) * x2 - sin(yaw) * y2;
    y22 = sin(yaw) * x2 + cos(yaw) * y2;
    z22 = z2;

    x33 = cos(yaw) * x3 - sin(yaw) * y3;
    y33 = sin(yaw) * x3 + cos(yaw) * y3;
    z33 = z3;

    x44 = cos(yaw) * x4 - sin(yaw) * y4;
    y44 = sin(yaw) * x4 + cos(yaw) * y4;
    z44 = z4;

    x55 = cos(yaw) * x5 - sin(yaw) * y5;
    y55 = sin(yaw) * x5 + cos(yaw) * y5;
    z55 = z5;

    x1 = x11;   y1 = y11;  z1 = z11;
    x2 = x22;   y2 = y22;  z2 = z22;
    x3 = x33;   y3 = y33;  z3 = z33;
    x4 = x44;   y4 = y44;  z4 = z44;
    x5 = x55;   y5 = y55;  z5 = z55;

    /************求解图像四角点在大地的投影**********/
    double X5, Y5, dX5, dY5;

    X1 = Xs - Height * x1 / z1;
    Y1 = Ys - Height * y1 / z1;

    X2 = Xs - Height * x2 / z2;
    Y2 = Ys - Height * y2 / z2;

    X3 = Xs - Height * x3 / z3;
    Y3 = Ys - Height * y3 / z3;

    X4 = Xs - Height * x4 / z4;
    Y4 = Ys - Height * y4 / z4;

    X5 = Xs - Height * x5 / z5;
    Y5 = Ys - Height * y5 / z5;

    dX5 = Height * x5 / z5;
    dY5 = Height * y5 / z5;
    //修正X
    double XX1, XX2, XX3, XX4, YY1, YY2, YY3, YY4, XX5, YY5;
    XX1 = Xs + Height * x1 / z1;
    YY1 = Ys + Height * y1 / z1;

    XX2 = Xs + Height * x2 / z2;
    YY2 = Ys + Height * y2 / z2;

    XX3 = Xs + Height * x3 / z3;
    YY3 = Ys + Height * y3 / z3;

    XX4 = Xs + Height * x4 / z4;
    YY4 = Ys + Height * y4 / z4;

    XX5 = Xs + Height * x5 / z5;
    YY5 = Xs + Height * y5 / z5;

    double arryxx[] = { XX1,XX2,XX3,XX4 };
    double arryyy[] = { YY1,YY2,YY3,YY4 };
    double MaxX, MinX, MaxY, MinY, MinXX, MaxXX, MinYY, MaxYY;
    struct JW CenterX;
    double arryx[] = { X1,X2,X3,X4 };
    double arryy[] = { Y1,Y2,Y3,Y4 };

    MaxX = GetDoubleMaxValue(arryx, 4);
    MinX = GetDoubleMinValue(arryx, 4);
    MaxY = GetDoubleMaxValue(arryy, 4);
    MinY = GetDoubleMinValue(arryy, 4);

    CenterX.J = (MaxX + MinX) / 2;
    CenterX.W = (MaxY + MinY) / 2;


    MaxXX = GetDoubleMaxValue(arryxx, 4);
    MinXX = GetDoubleMinValue(arryxx, 4);
    MaxYY = GetDoubleMaxValue(arryyy, 4);
    MinYY = GetDoubleMinValue(arryyy, 4);

    struct JW RU1, LD1, Center1;
    RU1 = XYToBL(MaxXX, MaxYY);
    LD1 = XYToBL(MinXX, MinYY);
    Center1.W = (RU1.W + LD1.W) / 2;

    struct JW RU, LD, Center;
    RU = XYToBL(MaxX, MaxY);
    LD = XYToBL(MinX, MinY);
    Center.J = (RU.J + LD.J) / 2;
    Center.W = Center1.W;
    Center1 = BLToXY(Center.W, Center.J);// 图像中心XY

    X1 = Center1.J + X1 - CenterX.J;
    Y1 = Center1.W + Y1 - CenterX.W;

    X2 = Center1.J + X2 - CenterX.J;
    Y2 = Center1.W + Y2 - CenterX.W;

    X3 = Center1.J + X3 - CenterX.J;
    Y3 = Center1.W + Y3 - CenterX.W;

    X4 = Center1.J + X4 - CenterX.J;
    Y4 = Center1.W + Y4 - CenterX.W;

    X5 = Center1.J + X5 - CenterX.J;
    Y5 = Center1.W + Y5 - CenterX.W;

    double arryx1[] = { X1,X2,X3,X4 };
    double arryy1[] = { Y1,Y2,Y3,Y4 };
    MaxX = GetDoubleMaxValue(arryx1, 4);
    MinX = GetDoubleMinValue(arryx1, 4);
    MaxY = GetDoubleMaxValue(arryy1, 4);
    MinY = GetDoubleMinValue(arryy1, 4);

    RU = XYToBL(MaxX, MaxY);
    LD = XYToBL(MinX, MinY);

    jwInfo.center = Center;
    jwInfo.pointLB = LD;
    //jwInfo.pointRB = 0;
    jwInfo.pointRU = RU;
    //jwInfo.pointLU = 0;

    //矩阵
    /****************计算图像大小***************/
    double GeoWidth, GeoHeight, scale;
    int dXY;
    int reqW, reqH;
    //	scale = f/(u*Height);//修改比例
    dXY = sqrt(dX5 * dX5 + dY5 * dY5);
    dXY = sqrt(Height * Height + dXY * dXY);
    scale = f / (u * dXY);
    //scale = 6.0;//f/(u*dXY);//采用固定比例，校正后图像有像物比例一致。
    GeoHeight = MaxX - MinX;
    GeoWidth = MaxY - MinY;

    reqH = GeoHeight * scale;//f/(u*Height);// 所需要的图像宽和高
    reqW = GeoWidth * scale;//f/(u*Height);

    //判断生成的图像是否过大，如果超过5000则放缩到5000
    float maxSize = 5000;
    if (reqH > reqW && reqH > maxSize)
    {
        scale = scale * (maxSize / reqH);
    }
    if (reqW > reqH && reqW > maxSize)
    {
        scale = scale * (maxSize / reqW);
    }
    reqH = GeoHeight * scale;
    reqW = GeoWidth * scale;

    /****************计算-像平面到投影平面-矩阵***************/

    CVPOINT2D32F dstpoint[4], srcpoint[4];

    X1 = X1 - MinX;    Y1 = Y1 - MinY;
    X2 = X2 - MinX;	 Y2 = Y2 - MinY;
    X3 = X3 - MinX;	 Y3 = Y3 - MinY;
    X4 = X4 - MinX;	 Y4 = Y4 - MinY;

    X5 = X5 - MinX;	 Y5 = Y5 - MinY;

    //变换到—宽X，高Y，图像坐标
    dstpoint[0].point_x_f = Y1 * scale;    dstpoint[0].point_y_f = X1 * scale;
    dstpoint[1].point_x_f = Y2 * scale;    dstpoint[1].point_y_f = X2 * scale;
    dstpoint[2].point_x_f = Y3 * scale;    dstpoint[2].point_y_f = X3 * scale;
    dstpoint[3].point_x_f = Y4 * scale;    dstpoint[3].point_y_f = X4 * scale;

    srcpoint[0].point_x_f = 0;           srcpoint[0].point_y_f = 0;
    srcpoint[1].point_x_f = ImgWidth;           srcpoint[1].point_y_f = 0;
    srcpoint[2].point_x_f = ImgWidth;           srcpoint[2].point_y_f = ImgHeight;
    srcpoint[3].point_x_f = 0;    srcpoint[3].point_y_f = ImgHeight;



    for (int i = 0; i < 4; i++)
    {
        (*srcpoint_out)[i] = srcpoint[i];
        (*dstpoint_out)[i] = dstpoint[i];
    }
    return jwInfo;
}




/**********************************Main**************************************/
//const INPUT_TESTParam &INPUT_TEST,OutputParam *const output
int JustifyPosition::getImageLocationInfo(const struct INPUT_TEST INPUT_TEST, struct CORRECTION_PARAM* const output)
{
    //init
    int init_results = InitParamBX(INPUT_TEST.nImageType, INPUT_TEST.fLongitude, INPUT_TEST.fLatitude, INPUT_TEST.fFlyHeight, INPUT_TEST.fTerrainHeight, INPUT_TEST.fPlanePitch, INPUT_TEST.fPlaneRoll, INPUT_TEST.fPlaneYaw, INPUT_TEST.fCameraPitch, INPUT_TEST.fCameraYaw, INPUT_TEST.fCameraFocus, 0.000006, 1920, 1080);
    if (init_results == 0)
    {
        CVPOINT2D32F dstpoint[4], srcpoint[4];
        //INPUT_TEST&output
        struct JWInfo result = GeoCorrectBX(&srcpoint, &dstpoint);
        output->left_longitude_f = result.pointLB.J;
        output->right_longitude_f = result.pointRU.J;
        output->top_latitude_f = result.pointRU.W;
        output->bottom_latitude_f = result.pointLB.W;

        //

        output->src_point1 = srcpoint[0];
        output->src_point2 = srcpoint[1];
        output->src_point3 = srcpoint[2];
        output->src_point4 = srcpoint[3];

        output->dst_point1 = dstpoint[0];
        output->dst_point2 = dstpoint[1];
        output->dst_point3 = dstpoint[2];
        output->dst_point4 = dstpoint[3];
        return 0;
    }
    else
    {
        //Internal initialization failed
        return -1;
    }
}
