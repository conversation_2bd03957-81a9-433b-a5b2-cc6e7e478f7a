﻿#include "DaoTiao.h"
#include "ui_DaoTiao.h"
#include <QDebug>
#include <QRegExp>
#include <QRegExpValidator>
#include <QScrollBar>
#include <QSpinBox>
#include <QLabel>
#include <QVBoxLayout>
#include <QTimer>
#include <QMutex>
#include <QTextCursor>
#include <QDateTime>
#include <QFontDatabase>
#include <QMetaType>
#include <QApplication>
#include <QStyle>

DaoTiao::<PERSON>oT<PERSON><PERSON>(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::DaoTiao),
    worker(new MessageWorker),
    logUpdateTimer(nullptr),
    connectState(false),
    dtWebRecvConnected(false),
    dtWebSndConnected(false)
{
    // 注册自定义元类型，用于跨线程信号槽通信
    qRegisterMetaType<QAbstractSocket::SocketError>("QAbstractSocket::SocketError");
    qRegisterMetaType<QAbstractSocket::SocketState>("QAbstractSocket::SocketState");

    ui->setupUi(this);
    
    // 设置窗口图标和标题
    setWindowTitle("消息中心");
    setWindowIcon(QApplication::style()->standardIcon(QStyle::SP_ComputerIcon));

    // 初始化日志更新定时器，定期刷新日志显示
    logUpdateTimer = new QTimer(this);
    logUpdateTimer->setInterval(LOG_UPDATE_INTERVAL);
    connect(logUpdateTimer, &QTimer::timeout, this, &DaoTiao::updateLogDisplay);
    logUpdateTimer->start();

    // 配置日志显示区域
    ui->daotiaoMsg->setAcceptRichText(true);
    ui->daotiaoMsg->document()->setMaximumBlockCount(2000); // 减少最大日志行数，以提高性能

    // 设置等宽字体，使日志显示更整齐
    QFont logFont = QFontDatabase::systemFont(QFontDatabase::FixedFont);
    logFont.setPointSize(9);
    ui->daotiaoMsg->setFont(logFont);

    // 设置日志显示样式
    ui->daotiaoMsg->document()->setDefaultStyleSheet(
        "body { color: #333; }"
        ".system { color: #008800; }"  // 系统消息为绿色
        ".error { color: #cc0000; font-weight: bold; }"  // 错误消息为红色加粗
        ".data { color: #0000aa; }"    // 数据消息为蓝色
        ".timestamp { color: #888; font-weight: normal; }"  // 时间戳为灰色
        ".highlight { background-color: #ffffcc; }"  // 高亮背景为淡黄色
    );

    // 菜单栏连接
    connect(ui->actionClearLogs, &QAction::triggered, this, &DaoTiao::clearLogs);
    connect(ui->actionExit, &QAction::triggered, this, &QApplication::quit);
    connect(ui->actionAbout, &QAction::triggered, this, [this]() {
        QMessageBox::about(this, "关于消息中心", 
            "<h3>消息中心 v1.0</h3>"
            "<p>消息处理中心应用程序</p>"
            "<p>实现WebSocket和UDP组播消息的转发与处理</p>");
    });

    // 配置工作线程
    worker->moveToThread(&workerThread);  // 将worker对象移动到工作线程

    // 连接信号和槽
    connect(&workerThread, &QThread::finished, worker, &QObject::deleteLater);  // 线程结束时清理worker
    connect(worker, &MessageWorker::messageReceived, this, &DaoTiao::handleMessage, Qt::QueuedConnection);
    connect(worker, &MessageWorker::errorOccurred, this, &DaoTiao::handleError, Qt::QueuedConnection);
    connect(worker, &MessageWorker::connectionStatusChanged, this, &DaoTiao::updateConnectionStatus, Qt::QueuedConnection);
    connect(worker, &MessageWorker::throughputUpdated, this, &DaoTiao::updateThroughput, Qt::QueuedConnection);
    connect(worker, &MessageWorker::webSocketConnected, this, &DaoTiao::handleWebSocketConnected, Qt::QueuedConnection);
    connect(worker, &MessageWorker::webSocketDisconnected, this, &DaoTiao::handleWebSocketDisconnected, Qt::QueuedConnection);
    connect(ui->daotiao, &QPushButton::clicked, this, &DaoTiao::onConnect);

    // 创建IP地址和端口号的输入验证器
    QRegExp ipPortRegExp(R"((\d{1,3}\.){3}\d{1,3}:\d{1,5})");
    QRegExpValidator* validator = new QRegExpValidator(ipPortRegExp);

    // 设置默认的连接参数
    ui->daotiaoReceive->setValidator(validator);
    ui->daotiaoReceive->setText("*************:1246");
    ui->uavSend->setValidator(validator);
    ui->uavSend->setText("*********:9001");
    ui->uavReceive->setValidator(validator);
    ui->uavReceive->setText("***********:7001");
    ui->zaiheReceive->setValidator(validator);
    ui->zaiheReceive->setText("*********:7702");
    ui->daotiaoSend->setValidator(validator);
    ui->daotiaoSend->setText("*************:1245");

    // 连接采样率控件
    connect(ui->msgSampleSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
        [this](int value) { worker->setMessageSamplingRate(value); });
    connect(ui->logSampleSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
        [this](int value) { worker->setLogSamplingRate(value); });

    // 显示初始化完成消息
    handleMessage(QString("[%1] <span class='system'>[系统] 程序已启动，等待连接...</span>")
                 .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
}

DaoTiao::~DaoTiao()
{
    // 停止工作线程中的任务
    if (worker) {
        QMetaObject::invokeMethod(worker, &MessageWorker::stop, Qt::QueuedConnection);
    }

    // 安全停止工作线程
    const int TIMEOUT_MS = 3000; // 3秒超时
    workerThread.quit();
    if (!workerThread.wait(TIMEOUT_MS)) {
        qWarning("DaoTiao worker thread failed to quit in %d ms, forcing termination", TIMEOUT_MS);
        workerThread.terminate();
        workerThread.wait();
    }

    // 清理定时器资源
    if (logUpdateTimer) {
        logUpdateTimer->stop();
        delete logUpdateTimer;
        logUpdateTimer = nullptr;
    }

    // 清理UI资源
    delete ui;
}

void DaoTiao::onConnect()
{
    if(connectState) {
        // 断开连接
        QMetaObject::invokeMethod(worker, &MessageWorker::stop, Qt::QueuedConnection);

        QString state = "连接";
        ui->daotiao->setText(state);
        handleMessage(QString("[%1] [系统] 已断开连接")
                     .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
    } else {
        // 确保工作线程已启动
        if (!workerThread.isRunning()) {
            workerThread.start();
        }

        // 获取连接参数
        QString dtRecvUrl = "ws://" + ui->daotiaoReceive->text();
        QString dtSndUrl = "ws://" + ui->daotiaoSend->text();

        QStringList tmp = ui->uavSend->text().split(":");
        QString uavSndIP = tmp[0];
        QString uavSndPort = tmp[1];

        tmp = ui->uavReceive->text().split(":");
        QString uavRecvIP = tmp[0];
        QString uavRecvPort = tmp[1];

        tmp = ui->zaiheReceive->text().split(":");
        QString zhRecvIP = tmp[0];
        QString zhRecvPort = tmp[1];

        // 启动工作线程并配置连接参数
        QMetaObject::invokeMethod(worker, [this, dtRecvUrl, dtSndUrl,
                                         uavSndIP, uavSndPort,
                                         uavRecvIP, uavRecvPort,
                                         zhRecvIP, zhRecvPort]() {
            worker->setAddresses(dtRecvUrl, dtSndUrl,
                               uavSndIP, uavSndPort,
                               uavRecvIP, uavRecvPort,
                               zhRecvIP, zhRecvPort);
            worker->start();
        }, Qt::QueuedConnection);

        // 更新UI状态
        QString state = "断开连接";
        ui->daotiao->setText(state);
        handleMessage(QString("[%1] [系统] 正在建立连接...")
                     .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
    }
    connectState = !connectState;
}

void DaoTiao::handleMessage(const QString &message)
{
    // 使用互斥锁保护日志缓冲区
    QMutexLocker locker(&logMutex);
    if (logBuffer.size() >= MAX_LOG_BUFFER) {
        logBuffer.dequeue();  // 如果缓冲区满，移除最旧的消息
    }

    // 将消息添加到缓冲区
    logBuffer.enqueue(message);
}

void DaoTiao::handleError(const QString &error)
{
    // 使用互斥锁保护日志缓冲区
    QMutexLocker locker(&logMutex);
    if (logBuffer.size() >= MAX_LOG_BUFFER) {
        logBuffer.dequeue();
    }

    // 将错误消息添加到缓冲区，使用CSS样式标记
    logBuffer.enqueue("<span class='error'>" + error + "</span>");
}

void DaoTiao::updateLogDisplay()
{
    if(worker->logCounter > 500){
        worker->logCounter = 0;
        logBuffer.clear();
        ui->daotiaoMsg->clear();
    }

    // 使用互斥锁保护日志缓冲区
    QMutexLocker locker(&logMutex);
    if (logBuffer.isEmpty()) return;

    // 限制每次更新的消息数量，避免UI卡顿
    const int MAX_MESSAGES_PER_UPDATE = 50;
    int messagesToProcess = qMin(logBuffer.size(), MAX_MESSAGES_PER_UPDATE);

    // 合并待显示的日志消息
    QString allLogs;
    for (int i = 0; i < messagesToProcess; i++) {
        allLogs += logBuffer.dequeue() + "<br>";
    }

    // 保持滚动位置
    QScrollBar* scrollBar = ui->daotiaoMsg->verticalScrollBar();
    bool atBottom = scrollBar->value() >= scrollBar->maximum() - 10;

    // 添加新日志
    ui->daotiaoMsg->moveCursor(QTextCursor::End);

    ui->daotiaoMsg->insertHtml(allLogs);

    // 如果之前在底部，保持滚动到底部
    if (atBottom) {
        scrollBar->setValue(scrollBar->maximum());
    }
}

void DaoTiao::updateConnectionStatus(bool connected)
{
    // 更新状态指示器样式
    QString style = connected ? "background-color: lime; border-radius: 8px;"
                            : "background-color: red; border-radius: 8px;";
    ui->daotiaoLED->setStyleSheet(style);

    // 记录状态变更日志
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    if (connected) {
        handleMessage(QString("[%1] <span class='system'>[系统] 所有连接已建立</span>").arg(timestamp));
    } else {
        handleMessage(QString("[%1] <span class='system'>[系统] 连接状态已变更</span>").arg(timestamp));
    }
}

void DaoTiao::updateThroughput(int messagesPerSecond, int packetsPerSecond, double avgLatency)
{
    ui->lblThroughput->setText(QString("<b>消息吞吐量:</b> %1 消息/秒")
                              .arg(messagesPerSecond));
    
    ui->lblLatency->setText(QString("<b>平均延迟:</b> %1 ms")
                           .arg(avgLatency, 0, 'f', 2));
    
    // 更新状态栏显示数据包信息
    statusBar()->showMessage(QString("数据包: %1 包/秒").arg(packetsPerSecond));
}

void DaoTiao::handleWebSocketConnected()
{
    QMutexLocker locker(&logMutex);
    if (logBuffer.size() >= MAX_LOG_BUFFER) {
        logBuffer.dequeue();
    }
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    logBuffer.enqueue(QString("[%1] <span class='system'>[系统] WebSocket已连接</span>").arg(timestamp));
}

void DaoTiao::handleWebSocketDisconnected()
{
    QMutexLocker locker(&logMutex);
    if (logBuffer.size() >= MAX_LOG_BUFFER) {
        logBuffer.dequeue();
    }
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    logBuffer.enqueue(QString("[%1] <span class='system'>[系统] WebSocket已断开</span>").arg(timestamp));
}

// 添加清除日志的方法
void DaoTiao::clearLogs()
{
    logBuffer.clear();
    ui->daotiaoMsg->clear();
    handleMessage(QString("[%1] [系统] 日志已清除")
                 .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")));
}

