﻿#ifndef QINGBAOWORKER_H
#define QINGBAOWORKER_H

#include <QObject>
#include <QUdpSocket>
#include <QTimer>
#include <QThread>
#include <QQueue>
#include <QMutex>
#include <QWaitCondition>
#include <QElapsedTimer>
#include <QJsonDocument>
#include <QJsonObject>
#include <QFile>
#include <QAtomicInt>
#include <QSemaphore>
#include <QThreadPool>
#include <QDir>
#include <memory>
#include "JustifyPosition.h"

// 前向声明ffmpeg相关结构体
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/imgutils.h>
#include <libavutil/opt.h>
#include <libswscale/swscale.h>
}
/**
 * @brief 飞机数据结构
 * 用于存储和处理飞机的位置和姿态信息
 */
struct PlaneData {
    double longitude;  ///< 经度（度）
    double latitude;   ///< 纬度（度）
    double height;     ///< 高度（米）
    double pitch;      ///< 俯仰角（度）
    double roll;       ///< 横滚角（度）
    double yaw;        ///< 偏航角（度）
    double loadPitch;  ///< 载荷俯仰角（度）
    double loadYaw;    ///< 载荷偏航角（度）
    double loadFov;    ///< 载荷视场角（度）
};
// 数据包定义
struct DataPacket {
    enum Type {
        JSON,
        IMAGE,
        CCD,
        IR,
        UNKNOWN
    };
    
    Type type;
    QString filePath;
    QByteArray data;
    QJsonObject jsonData;
    bool isProcessed = false;
    // 矫正定位相关
    struct PlaneData planinfo;

    // 构造函数
    DataPacket() : type(UNKNOWN) {}
    explicit DataPacket(const QByteArray& jsonData);
};



// 编码器上下文
struct EncoderContext {
    AVCodecContext *codecContext = nullptr;
    AVFrame *frame = nullptr;
    SwsContext *swsContext = nullptr;
    int frameCount = 0;
    
    ~EncoderContext() {
        if (swsContext) sws_freeContext(swsContext);
        if (frame) av_frame_free(&frame);
        if (codecContext) avcodec_free_context(&codecContext);
    }
};

/**
 * @brief 情报处理工作类，使用生产者-消费者模式处理UDP数据
 */
class QingBaoWorker : public QObject
{
    Q_OBJECT

public:
    explicit QingBaoWorker(QObject *parent = nullptr);
    ~QingBaoWorker();

    /**
     * @brief 设置UDP组播地址
     * @param address 格式为"IP:端口"的组播地址
     */
    void setMulticastAddress(const QString &address);

    /**
     * @brief 设置默认目标UDP地址
     * @param address 格式为"IP:端口"的目标地址
     */
    void setTargetAddress(const QString &address);
    
    /**
     * @brief 设置图像目标UDP地址
     * @param address 格式为"IP:端口"的图像目标地址
     */
    void setImageTargetAddress(const QString &address);
    
    /**
     * @brief 设置CCD视频目标UDP地址
     * @param address 格式为"IP:端口"的CCD视频目标地址
     */
    void setCcdTargetAddress(const QString &address);
    
    /**
     * @brief 设置IR视频目标UDP地址
     * @param address 格式为"IP:端口"的IR视频目标地址
     */
    void setIrTargetAddress(const QString &address);
    
    /**
     * @brief 设置消息采样率
     * @param rate 采样率 (1表示处理所有消息, 2表示处理一半消息, 以此类推)
     */
    void setSamplingRate(int rate);
    
    /**
     * @brief 设置日志采样率
     * @param rate 日志采样率
     */
    void setLogSamplingRate(int rate);

public slots:
    /**
     * @brief 启动工作
     */
    void start();
    
    /**
     * @brief 停止工作
     */
    void stop();

signals:
    /**
     * @brief 接收到消息信号
     * @param message 消息内容
     */
    void messageReceived(const QString &message);
    
    /**
     * @brief 发生错误信号
     * @param error 错误信息
     */
    void errorOccurred(const QString &error);
    
    /**
     * @brief 连接状态变化信号
     * @param connected 是否已连接
     */
    void connectionStatusChanged(bool connected);
    
    /**
     * @brief 性能统计更新信号
     * @param messagesPerSecond 每秒处理消息数
     * @param avgProcessTime 平均处理时间(毫秒)
     */
    void performanceUpdated(int messagesPerSecond, double avgProcessTime);

private slots:
    /**
     * @brief 处理接收到的UDP数据
     */
    void onUdpReadyRead();
    
    /**
     * @brief 更新性能统计
     */
    void updatePerformanceStats();
void sendIrDataBuffer();
void sendCCdDataBuffer();
private:
    // 网络相关
    QUdpSocket *udpSocket;                 // 接收组播数据的UDP套接字
    QUdpSocket *targetSocket;              // 通用目标UDP套接字 
    QUdpSocket *imageSocket;               // 图像目标UDP套接字
    QUdpSocket *ccdSocket;                 // CCD视频目标UDP套接字
    QUdpSocket *irSocket;                  // IR视频目标UDP套接字
    
    QString multicastAddress;              // 组播地址
    int multicastPort;                     // 组播端口
    
    QString targetHost;                    // 目标主机
    int targetPort;                        // 目标端口
    
    QString imageHost;                     // 图像目标主机
    int imagePort;                         // 图像目标端口
    
    QString ccdHost;                       // CCD视频目标主机
    int ccdPort;                           // CCD视频目标端口
    
    QString irHost;                        // IR视频目标主机
    int irPort;                            // IR视频目标端口
    
    // 线程相关
    bool running;                          // 运行标志
    QMutex queueMutex;                     // 队列互斥锁
    QWaitCondition queueNotEmpty;          // 队列非空条件变量
    QQueue<DataPacket> packetQueue;        // 数据包队列
    QVector<QThread*> workerThreads;       // 工作线程数组
    
    // 统计相关
    QTimer *statsTimer;                    // 统计定时器
    QElapsedTimer elapsedTimer;            // 时间测量计时器
    QAtomicInt messageCount;               // 消息计数器
    QAtomicInt processedCount;             // 已处理消息计数器
    QQueue<double> processingTimes;        // 处理时间队列
    QMutex statsMutex;                     // 统计互斥锁
    
    QQueue<QByteArray> dataIrBuff;
    QTimer *dataIrBufferTimer;
    QQueue<QByteArray> dataCCdBuff;
    QTimer *dataCCdBufferTimer;

    // 采样控制
    int samplingRate;                      // 消息采样率
    int logSamplingRate;                   // 日志采样率
    QAtomicInt messageCounter;             // 消息计数器(用于采样)


    // 矫正
    JustifyPosition jz;
    struct LOCAL_DATA_FORMAT save_dat;

    // GPU加速相关
    bool gpuAcceleration;                  // 是否启用GPU加速
    
    // 处理函数
    void processPacket(const DataPacket &packet);
    
    // 初始化函数
    bool initializeUdpSocket();
    void initializeFFmpeg();
    bool checkGpuAcceleration();
    
    // 消费者线程函数
    void consumerThreadFunction();
    
    // 分发日志消息
    void logMessage(const QString &message);
    void logError(const QString &error);
    bool shouldLogMessage();

    // 图像和视频处理函数
    bool processImage(const DataPacket &packet);
    bool processCcdVideo(const DataPacket &packet);
    bool processIrVideo(const DataPacket &packet);
    
    // 编码器相关
    std::unique_ptr<EncoderContext> createEncoder(int width, int height, bool isCcd);
    bool encodeFrame(std::unique_ptr<EncoderContext> &encoderCtx, const QByteArray &imageData, QByteArray &outPacket);
    
    // 数据发送函数
    bool sendPacket(QUdpSocket *socket, const QString &host, int port, const QByteArray &data, const QString &type, int maxPacketSize = 1024);
    
    // 常量
    static const int MAX_PACKET_SIZE = 1024;    // 最大数据包大小
    static const int MAX_QUEUE_SIZE = 50000;          // 最大队列大小
    static const int MAX_TIME_SAMPLES = 100;         // 最大时间样本数
    static const int DEFAULT_NUM_THREADS = 4;        // 默认线程数
    static const int UDP_MTU = 1500;                 // UDP最大传输单元
    uint32_t photo_count = 0;
public:
        QAtomicInt logCounter;                 // 日志计数器(用于采样)
};

#endif // QINGBAOWORKER_H
