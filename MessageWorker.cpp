﻿#include "MessageWorker.h"
#include <QJsonDocument>
#include <QJsonParseError>
#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>
#include <QCoreApplication>
#include <QThread>
#include <QDateTime>
#include <QMetaType>
#include <QtMath>
#include <QFile>
#include <QDir>
MessageWorker::MessageWorker(QObject *parent)
    : QObject(parent)
    , isRunning(false)
    , dtWebRecvConnected(false)
    , dtWebSndConnected(false)
    , messageCount(0)
    , packetCount(0)
    , totalLatency(0)
    , messageSamplingRate(1)  // 改为处理所有消息
    , logSamplingRate(10)
    , messageCounter(0)
    , logCounter(0)
    , throughputTimer(nullptr)
    , batchTimer(nullptr)
    , reconnectTimer(nullptr)
    , routeTimer(nullptr)
    , dtWebRecv(nullptr)
    , dtWebSnd(nullptr)
{
    // 注册元类型，以支持跨线程信号槽
    qRegisterMetaType<QAbstractSocket::SocketError>("QAbstractSocket::SocketError");
    qRegisterMetaType<QAbstractSocket::SocketState>("QAbstractSocket::SocketState");
    
    // 初始化延迟计时器
    latencyTimer.start();

    // 注意：socket对象会在start()方法中创建，确保在正确的线程中

}

MessageWorker::~MessageWorker()
{
    stop();
    
    // 确保删除定时器
    if (throughputTimer) {
        throughputTimer->stop();
        delete throughputTimer;
        throughputTimer = nullptr;
    }
    
    if (batchTimer) {
        batchTimer->stop();
        delete batchTimer;
        batchTimer = nullptr;
    }
    
    if (reconnectTimer) {
        reconnectTimer->stop();
        delete reconnectTimer;
        reconnectTimer = nullptr;
    }
    
    if (routeTimer) {
        routeTimer->stop();
        delete routeTimer;
        routeTimer = nullptr;
    }
    // 清理socket对象
    deleteSocketObjects();
}

// 辅助方法，添加时间戳并发送日志
void MessageWorker::emitLogMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    emit messageReceived(QString("[%1] %2").arg(timestamp).arg(message));
}

// 辅助方法，添加时间戳并发送错误
void MessageWorker::emitErrorMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    emit errorOccurred(QString("[%1] %2").arg(timestamp).arg(message));
}

// 创建所有socket对象
void MessageWorker::createSocketObjects()
{
    // WebSocket对象
    dtWebRecv = new QWebSocket();
    dtWebSnd = new QWebSocket();
    
    // UDP socket对象
    uavUdpSnd = new QUdpSocket();
    uavUdpRecv = new QUdpSocket();
    zhUdpRecv = new QUdpSocket();
    routeRecv = new QUdpSocket();
    // 连接WebSocket信号
    connect(dtWebRecv, &QWebSocket::connected, this, [this]() {
        dtWebRecvConnected = true;
        emit webSocketConnected();
        emit connectionStatusChanged(dtWebRecvConnected && dtWebSndConnected);
        emitLogMessage("[系统] 导调接收WebSocket已连接: " + dtRecvUrl);
    });

    connect(dtWebRecv, &QWebSocket::disconnected, this, [this]() {
        dtWebRecvConnected = false;
        emit webSocketDisconnected();
        emit connectionStatusChanged(dtWebRecvConnected && dtWebSndConnected);
        emitLogMessage("[系统] 导调接收WebSocket已断开: " + dtRecvUrl);
    });

    connect(dtWebSnd, &QWebSocket::connected, this, [this]() {
        dtWebSndConnected = true;
        emit webSocketConnected();
        emit connectionStatusChanged(dtWebRecvConnected && dtWebSndConnected);
        emitLogMessage("[系统] 导调发送WebSocket已连接: " + dtSndUrl);
    });

    connect(dtWebSnd, &QWebSocket::disconnected, this, [this]() {
        dtWebSndConnected = false;
        emit webSocketDisconnected();
        emit connectionStatusChanged(dtWebRecvConnected && dtWebSndConnected);
        emitLogMessage("[系统] 导调发送WebSocket已断开: " + dtSndUrl);
    });

    connect(dtWebRecv, &QWebSocket::textMessageReceived, this, &MessageWorker::processWebSocketMessage);
    
    // 处理错误信号
    connect(dtWebRecv, QOverload<QAbstractSocket::SocketError>::of(&QWebSocket::error),
            this, [this]() {
        emitErrorMessage("[错误] 导调接收WebSocket错误: " + dtWebRecv->errorString());
    });
    
    connect(dtWebSnd, QOverload<QAbstractSocket::SocketError>::of(&QWebSocket::error),
            this, [this]() {
        emitErrorMessage("[错误] 导调发送WebSocket错误: " + dtWebSnd->errorString());
    });
    
    connect(uavUdpRecv, &QUdpSocket::readyRead, this, &MessageWorker::processUdpMessage);
    connect(zhUdpRecv, &QUdpSocket::readyRead, this, &MessageWorker::processUdpMessage);
    connect(routeRecv, &QUdpSocket::readyRead, this, &MessageWorker::processUdpMessage);
}

// 删除所有socket对象
void MessageWorker::deleteSocketObjects()
{
    if (dtWebRecv) {
        dtWebRecv->close();
        dtWebRecv->deleteLater();
        dtWebRecv = nullptr;
    }
    
    if (dtWebSnd) {
        dtWebSnd->close();
        dtWebSnd->deleteLater();
        dtWebSnd = nullptr;
    }
    
    if (uavUdpSnd) {
        uavUdpSnd->close();
        uavUdpSnd->deleteLater();
        uavUdpSnd = nullptr;
    }
    
    if (uavUdpRecv) {
        uavUdpRecv->close();
        uavUdpRecv->deleteLater();
        uavUdpRecv = nullptr;
    }
    
    if (zhUdpRecv) {
        zhUdpRecv->close();
        zhUdpRecv->deleteLater();
        zhUdpRecv = nullptr;
    }

    if (routeRecv) {
        routeRecv->close();
        routeRecv->deleteLater();
        routeRecv = nullptr;
    }
}

void MessageWorker::setAddresses(const QString &dtRecvUrl, const QString &dtSndUrl,
                                 const QString &uavSndIP, const QString &uavSndPort,
                                 const QString &uavRecvIP, const QString &uavRecvPort,
                                 const QString &zhRecvIP, const QString &zhRecvPort)
{
    QMutexLocker locker(&mutex);
    this->dtRecvUrl = dtRecvUrl;
    this->dtSndUrl = dtSndUrl;
    this->uavSndIP = uavSndIP;
    this->uavSndPort = uavSndPort;
    this->uavRecvIP = uavRecvIP;
    this->uavRecvPort = uavRecvPort;
    this->zhRecvIP = zhRecvIP;
    this->zhRecvPort = zhRecvPort;
}

void MessageWorker::setMessageSamplingRate(int rate)
{
    QMutexLocker locker(&mutex);
    messageSamplingRate = qMax(1, rate); // 确保至少是1
    emitLogMessage(QString("[系统] 消息采样率已设置为 1/%1").arg(messageSamplingRate));
}

void MessageWorker::setLogSamplingRate(int rate)
{
    QMutexLocker locker(&mutex);
    logSamplingRate = qMax(1, rate); // 确保至少是1
    emitLogMessage(QString("[系统] 日志采样率已设置为 1/%1").arg(logSamplingRate));
}

void MessageWorker::start()
{
    if (!isRunning) {
        isRunning = true;
        emitLogMessage("[系统] 正在启动消息处理系统...");
        // 创建socket对象
        createSocketObjects();
        // 初始化定时器 - 确保在当前线程中创建
        if (!throughputTimer) {
            throughputTimer = new QTimer(this);
            throughputTimer->setInterval(1000);
            connect(throughputTimer, &QTimer::timeout, this, &MessageWorker::updateThroughput);
        }

        if (!reconnectTimer) {
            reconnectTimer = new QTimer(this);
            reconnectTimer->setInterval(WEBSOCKET_CHECK_INTERVAL_MS);
            connect(reconnectTimer, &QTimer::timeout, this, &MessageWorker::checkWebSocketConnection);
        }
        // 不让航线定时器自动启动，等接收到曾老师发来的指令，再进行启动
        //        if (!routeTimer && !waypointFilePath.isEmpty()) {
        //            routeTimer = new QTimer(this);
        //            routeTimer->setInterval(1000);
        //            connect(routeTimer, &QTimer::timeout, this, &MessageWorker::checkRouteFile);
        //            routeTimer->start();
        //            emitLogMessage("[系统] 航点读取定时器已启动，间隔: 1秒钟");
        //            // 立即执行一次读取操作
        //            QTimer::singleShot(1000, this, &MessageWorker::checkRouteFile);
        //        }
        
        try {
            // 在当前线程中打开WebSocket连接
            dtWebRecv->open(QUrl(dtRecvUrl));
            dtWebSnd->open(QUrl(dtSndUrl));
            emitLogMessage("[系统] WebSocket连接初始化成功");
        } catch (const std::exception& e) {
            emitErrorMessage(QString("[错误] WebSocket初始化失败: %1").arg(e.what()));
        }

        // 初始化UDP组播
        try {
            initUdpMulticast(uavUdpSnd, uavSndIP, uavSndPort);
            emitLogMessage(QString("[系统] 无人机发送UDP组播初始化成功: %1:%2").arg(uavSndIP).arg(uavSndPort));
        } catch (const std::exception& e) {
            emitErrorMessage(QString("[错误] 无人机发送UDP组播初始化失败: %1").arg(e.what()));
        }
        
        try {
            initUdpMulticast(uavUdpRecv, uavRecvIP, uavRecvPort);
            emitLogMessage(QString("[系统] 无人机接收UDP组播初始化成功: %1:%2").arg(uavRecvIP).arg(uavRecvPort));
        } catch (const std::exception& e) {
            emitErrorMessage(QString("[错误] 无人机接收UDP组播初始化失败: %1").arg(e.what()));
        }
        
        try {
            initUdpMulticast(zhUdpRecv, zhRecvIP, zhRecvPort);
            emitLogMessage(QString("[系统] 载荷接收UDP组播初始化成功: %1:%2").arg(zhRecvIP).arg(zhRecvPort));
        } catch (const std::exception& e) {
            emitErrorMessage(QString("[错误] 载荷接收UDP组播初始化失败: %1").arg(e.what()));
        }
        try {
            initUdpMulticast(routeRecv, "*********", "9202");
            emitLogMessage(QString("[系统] 航线接收UDP组播初始化成功: %1:%2").arg("*********").arg("9202"));
        } catch (const std::exception& e) {
            emitErrorMessage(QString("[错误] 航线接收UDP组播初始化失败: %1").arg(e.what()));
        }
        // 启动定时器
        throughputTimer->start();
        reconnectTimer->start();
        
        // 重置计数器
        messageCount = 0;
        packetCount = 0;
        totalLatency = 0;
        
        // 使当前线程针对高频率消息进行优化
        QThread::currentThread()->setPriority(QThread::HighPriority);
        
        emitLogMessage("[系统] 消息处理系统已启动");
    }
}

void MessageWorker::stop()
{
    if (isRunning) {
        isRunning = false;
        emitLogMessage("[系统] 正在停止消息处理系统...");
        
        // 停止所有连接
        deleteSocketObjects();

        // 停止定时器
        if (throughputTimer) {
            throughputTimer->stop();
        }
        
        if (batchTimer) {
            batchTimer->stop();
        }
        
        if (reconnectTimer) {
            reconnectTimer->stop();
        }

        if (routeTimer) {
            routeTimer->stop();
        }

        // 清空消息队列和批处理
        pendingMessages.clear();
        messageBatches.clear();
        
        emitLogMessage("[系统] 消息处理系统已停止");
    }
}

void MessageWorker::initUdpMulticast(QUdpSocket *udpSocket, const QString &ip, const QString &port)
{
    // 设置socket缓冲区大小，以处理高频率消息
    udpSocket->setSocketOption(QAbstractSocket::ReceiveBufferSizeSocketOption, 1024 * 1024);
    udpSocket->setSocketOption(QAbstractSocket::SendBufferSizeSocketOption, 1024 * 1024);
    
    if (!udpSocket->bind(QHostAddress::AnyIPv4, port.toUShort(), QUdpSocket::ShareAddress | QUdpSocket::ReuseAddressHint)) {
        throw std::runtime_error(udpSocket->errorString().toStdString());
    }
    
    QHostAddress multicastGroupAddress(ip);
    if (!udpSocket->joinMulticastGroup(multicastGroupAddress)) {
        throw std::runtime_error(udpSocket->errorString().toStdString());
    }
}

bool MessageWorker::shouldLogMessage()
{
    return (++logCounter % logSamplingRate) == 0;
}

void MessageWorker::processWebSocketMessage(const QString &message)
{
    {
        QMutexLocker locker(&mutex);
        if (!isRunning) return;

        // 消息采样 - 只处理一部分消息
        if ((++messageCounter % messageSamplingRate) != 0) {
            return;
        }

        // 解析发来的json数据头，如果是水上目标信息，就转发给载荷组播，否则就直接发给曾老师

        QByteArray data = message.toUtf8();
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(data, &error);

        if (error.error == QJsonParseError::NoError){
            QJsonObject obj = doc.object();
            if(obj.contains("MESSAGETYPE")){
                QString msg = obj.value("MESSAGETYPE").toString();
                if(msg == "SNR_SURFACE_PLATFORM_RUNTIME_REPORT"){
                    QByteArray waterInfo = doc.toJson(QJsonDocument::Compact);
                    zhUdpRecv->writeDatagram(waterInfo, QHostAddress(zhRecvIP), 6602);
                }
                else if(msg == "SNR_AREA_INFO"){
                    // 现在处理逻辑为，将区域信息发送到指定组播ip与端口
                    QJsonArray info_obj = obj.value("STAREAVERTEXES").toArray();
                    if(info_obj.isEmpty()) return;
                    QJsonDocument info(info_obj);

                    QByteArray area_info = info.toJson();
                    routeRecv->writeDatagram(area_info, QHostAddress("*********"), 9201);

                    // 将航点信息保存到文件中，因业务逻辑调整，先注释
                    //                    if(info_obj.size() > 0){
                    //                        QJsonDocument info_doc(info_obj);
                    //                        QString filePath = QDir::currentPath() + "/111.json";
                    //                        QFile file(filePath);
                    //                        if(file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                    //                            file.write(info_doc.toJson());
                    //                            file.close();
                    //                        }
                    //                    }
                }
            } else if (obj.contains("stAesheader")){
                QString msg = obj.value("stAesheader").toString();
                if(msg == "SNR_AIR_PLATFORM_INHERENT_PROPERTY"){
                    ENTITYID=obj.value("czPlatformEntityID").toString();//ID
//                    ENTITYMODEL=obj.value("czPlatformEntityMode").toString();//
                    ENTITYMODEL="UAV";
                    ENTITYTYPE=obj.value("czPlatformEntityType").toString();
                    IDENTITYFICATION="5";
                }
                uavUdpSnd->writeDatagram(data, QHostAddress(uavSndIP), uavSndPort.toUShort());
            }
        }
        
        // 更新性能计数
        messageCount++;
        
        // 记录延迟
        totalLatency += latencyTimer.elapsed();
        latencyTimer.restart();
        
        // 记录日志
        if (shouldLogMessage()) {
            emitLogMessage("[发送至无人机] " + message);
        }
    }
}

void MessageWorker::processUdpMessage()
{
    QUdpSocket *socket = qobject_cast<QUdpSocket*>(sender());
    if (!socket) return;
    
    QMutexLocker locker(&mutex);
    if (!isRunning) return;
    
    while (socket->hasPendingDatagrams()) {
        QByteArray datagram;
        datagram.resize(socket->pendingDatagramSize());
        QHostAddress sender;
        quint16 senderPort;
        socket->readDatagram(datagram.data(), datagram.size(), &sender, &senderPort);
        
        // 消息采样 - 只处理一部分消息
        if ((++messageCounter % messageSamplingRate) != 0) {
            continue;
        }
        
        // 更新性能计数
        packetCount++;
        
        // 根据socket类型直接处理消息
        if (socket == uavUdpRecv) {
            // 二进制转JSON
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(datagram, &error);
            if (error.error == QJsonParseError::NoError) {
                QString jsonStr = doc.toJson(QJsonDocument::Compact);
                if (dtWebSndConnected) {
                    dtWebSnd->sendTextMessage(jsonStr);
                    
                    // 记录日志（采样）
                    if (shouldLogMessage()) {
                        emitLogMessage("[转发无人机数据] " + jsonStr);
                    }
                }
            } else{

                // 在这里对uav发来的二进制数据进行修改并发送
                if(static_cast<unsigned char>(datagram.at(0))  == 0xEB && static_cast<unsigned char>(datagram.at(1)) == 0x90){
                    double lon = (static_cast<unsigned char>(datagram.at(12)) \
                                  | static_cast<unsigned char>(datagram.at(13)) << 8 \
                                  | static_cast<unsigned char>(datagram.at(14)) << 16 \
                                  | static_cast<unsigned char>(datagram.at(15)) << 24) * 180.0 / INT32_MAX;  // 经度

                    double lat = (static_cast<unsigned char>(datagram.at(16)) \
                                  | static_cast<unsigned char>(datagram.at(17)) << 8 \
                                  | static_cast<unsigned char>(datagram.at(18)) << 16 \
                                  | static_cast<unsigned char>(datagram.at(19)) << 24) * 180.0 / INT32_MAX;  // 维度

                    double alt = (static_cast<unsigned char>(datagram.at(61)) \
                                  | static_cast<unsigned char>(datagram.at(62)) << 8)* 12000.0 / INT16_MAX;   // 高度

                    double pitch = (static_cast<unsigned char>(datagram.at(20)) \
                                    | static_cast<unsigned char>(datagram.at(21)) << 8)* 180.0 / INT16_MAX;   // 俯仰

                    double roll = (static_cast<unsigned char>(datagram.at(22)) \
                                   | static_cast<unsigned char>(datagram.at(23)) << 8)* 180.0 / INT16_MAX;    // 横滚
                    if (roll > 360) roll -= 360;

                    double yaw = (static_cast<unsigned char>(datagram.at(24)) \
                                  | static_cast<unsigned char>(datagram.at(25)) << 8)* 180.0 / INT16_MAX;     // 方位
                    if (yaw < 0) yaw += 360;

                    double Vx = (static_cast<unsigned char>(datagram.at(57)) \
                                 | static_cast<unsigned char>(datagram.at(58)) << 8)* 150.0 / INT16_MAX;    // 东向地速

                    double Vy = (static_cast<unsigned char>(datagram.at(59)) \
                                 | static_cast<unsigned char>(datagram.at(60)) << 8)* 150.0 / INT16_MAX;    // 北向地速

                    double Vz = (static_cast<unsigned char>(datagram.at(55)) \
                                 | static_cast<unsigned char>(datagram.at(56)) << 8)* 150.0 / INT16_MAX;    // 天向地速

                    int oil = (static_cast<unsigned char>(datagram.at(155)) \
                               | static_cast<unsigned char>(datagram.at(156)) << 8)* 600 / (UINT16_MAX + 1); // 油量

                    QJsonObject json;
                    json["MESSAGETYPE"] = "SNR_AIR_PLATFORM_RUNTIME_REPORT_BH";
                    json["TIME"] = 1;
                    json["ENTITYID"] = ENTITYID;
                    json["ENTITYMODEL"] = "UAV";
                    json["ENTITYTYPE"] = ENTITYTYPE;
                    json["IDENTIFICATION"] = IDENTITYFICATION;
                    json["LON"] = lon;
                    json["LAT"] = lat;
                    json["ALT"] = alt;
                    json["PITCH"] = pitch;
                    json["ROLL"] = roll;
                    json["YAW"] = yaw;
                    json["VX"] = Vx;
                    json["VY"] = Vy;
                    json["VZ"] = Vz;
                    json["OILMASS"] = oil;
                    QJsonDocument metaDoc(json);
                    QString metaData = metaDoc.toJson(QJsonDocument::Compact);

                    dtWebRecv->sendTextMessage(metaData);
                    if (shouldLogMessage()) {
                        emitLogMessage("[转发无人机数据] 解析后的无人机JSON数据: " + metaData);
                    }
                }else {
                    emitErrorMessage("[转发无人机数据]遥测信息同步字不正确");
                }
            }
        } else if (socket == zhUdpRecv) {
            // 直接转发载荷JSON
            if (dtWebSndConnected) {
                QString jsonStr = QString::fromUtf8(datagram);
                dtWebSnd->sendTextMessage(jsonStr);
                
                // 记录日志（采样）
                if (shouldLogMessage()) {
                    emitLogMessage("[转发载荷数据] " + jsonStr);
                }
            }
        } else if (socket == routeRecv) {
            if(datagram[0]=='O' && datagram[1]=='k'){
                routSendCnt = 5;
                QString filePath = QDir::currentPath() + "/debug/LinePoints.json";
                setWaypointFilePath(filePath);
            }
            // 二进制转JSON,先注释
            //            QJsonParseError error;
            //            QJsonDocument doc = QJsonDocument::fromJson(datagram, &error);
            //            if (error.error == QJsonParseError::NoError) {
            //                if(doc.isObject()){
            //                    QJsonObject jsonObj = doc.object();
            //                    if(jsonObj["none"] == "ok"){
            //                        QString filePath = QDir::currentPath() + "/debug/LinePoints.json";
            //                        setWaypointFilePath(filePath);
            //                    }
            //                }
            //            }
        }
    }
}

void MessageWorker::checkWebSocketConnection()
{
    QMutexLocker locker(&mutex);
    if (!isRunning) return;
    
    // 检查WebSocket连接状态
    if (!dtWebRecvConnected && !dtRecvUrl.isEmpty() && dtWebRecv) {
        emitLogMessage("[系统] 尝试重连导调接收WebSocket...");
        dtWebRecv->open(QUrl(dtRecvUrl));
    }
    
    if (!dtWebSndConnected && !dtSndUrl.isEmpty() && dtWebSnd) {
        emitLogMessage("[系统] 尝试重连导调发送WebSocket...");
        dtWebSnd->open(QUrl(dtSndUrl));
    }
}

// 设置航点文件路径
void MessageWorker::setWaypointFilePath(const QString &filePath)
{
    this->waypointFilePath = filePath;

    // 如果已启动，重新初始化航点定时器
    if (isRunning && !waypointFilePath.isEmpty()) {
        if (routeTimer) {
            routeTimer->stop();
            delete routeTimer;
        }

        routeTimer = new QTimer(this);
        routeTimer->setInterval(1000); // 1秒
        connect(routeTimer, &QTimer::timeout, this, &MessageWorker::checkRouteFile);
        routeTimer->start();
        emitLogMessage(QString("[系统] 航点文件路径已设置为: %1").arg(waypointFilePath));

        // 立即执行一次读取操作
        QTimer::singleShot(1000, this, &MessageWorker::checkRouteFile);
    }
}

// 读取航点文件并发送
void MessageWorker::checkRouteFile()
{
    QMutexLocker locker(&mutex);
    if (!isRunning || waypointFilePath.isEmpty() || !dtWebSndConnected) return;

    emitLogMessage("[系统] 正在读取航点文件...");

    try {
        QList<ROUTE_POINT> waypoints;
        QFile file(waypointFilePath);

        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            emitErrorMessage(QString("[错误] 无法打开航点文件: %1").arg(file.errorString()));
            return;
        }

        QByteArray jsonData = file.readAll();
        file.close();

        QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonData);
        QJsonArray jsonArray = jsonDoc.array();
        QJsonArray waypointsArray;
        for (auto i : jsonArray) {
            if(i.isObject()){
                QJsonObject waypointObj;
                QJsonObject tmp = i.toObject();
                waypointObj["uhRoutePointID"] = tmp["iLinePointNo"];
                waypointObj["fLongitude"] = tmp["Longtitude"];
                waypointObj["fLatitude"] = tmp["Latitude"];
                waypointObj["shAltitude"] = tmp["Height"];
                waypointsArray.append(waypointObj);
            }
        }
        //        QList<ROUTE_POINT> waypoints = parseWaypointFile(waypointFilePath);
        //        if (waypoints.isEmpty()) {
        //            emitErrorMessage("[错误] 航点文件解析失败或为空");
        //            return;
        //        }

        //        // 创建JSON数组
        //        QJsonArray waypointsArray;
        //        for (const ROUTE_POINT &wp : waypoints) {
        //            QJsonObject waypointObj;
        //            waypointObj["uhRoutePointID"] = wp.uhRoutePointID;
        //            waypointObj["fLongitude"] = wp.fLongitude;
        //            waypointObj["fLatitude"] = wp.fLatitude;
        //            waypointObj["shAltitude"] = wp.shAltitude;
        //            waypointsArray.append(waypointObj);
        //        }

        // 创建包含航点数组的JSON对象
        QJsonObject jsonMessage;
        jsonMessage["MESSAGETYPE"] = "waypoints";
        jsonMessage["TIMESTAMP"] = QDateTime::currentDateTime().toString(Qt::ISODate);
        jsonMessage["COUNT"] = waypointsArray.size();
        jsonMessage["WAYPOINTS"] = waypointsArray;

        // 转换为JSON文档并发送
        QJsonDocument doc(jsonMessage);
        QString jsonString = doc.toJson(QJsonDocument::Compact);

        // 发送到WebSocket
        if (dtWebSnd && dtWebSndConnected) {
            dtWebSnd->sendTextMessage(jsonString);
            // 发送次数达到5次，关闭计时器
            if(!routSendCnt --){
                file.remove();
                routeTimer->stop();
            }
            emitLogMessage(QString("[系统] 已发送%1个航点数据").arg(waypointsArray.size()));
        } else {
            emitErrorMessage("[错误] WebSocket未连接，无法发送航点数据");
        }
    } catch (const std::exception &e) {
        emitErrorMessage(QString("[错误] 航点文件处理失败: %1").arg(e.what()));
    }
}

// 解析航点文件
QList<ROUTE_POINT> MessageWorker::parseWaypointFile(const QString &filePath)
{
    QList<ROUTE_POINT> waypoints;
    QFile file(filePath);

    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        emitErrorMessage(QString("[错误] 无法打开航点文件: %1").arg(file.errorString()));
        return waypoints;
    }

    QTextStream in(&file);
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (line.isEmpty()) continue;

        QStringList parts = line.split(QRegExp("\\s+"), QString::SkipEmptyParts);
        if (parts.size() < 8) {
            emitErrorMessage(QString("[错误] 航点文件格式不正确，行内容: %1").arg(line));
            continue;
        }

        ROUTE_POINT wp;
        bool ok;

        // 解析航点编号
        wp.uhRoutePointID = parts[0].toInt(&ok);
        if (!ok) continue;

        // 解析经度（度分秒）
        int lonDeg = parts[1].toInt(&ok);
        if (!ok) continue;

        int lonMin = parts[2].toInt(&ok);
        if (!ok) continue;

        double lonSec = parts[3].toDouble(&ok);
        if (!ok) continue;

        // 解析纬度（度分秒）
        int latDeg = parts[4].toInt(&ok);
        if (!ok) continue;

        int latMin = parts[5].toInt(&ok);
        if (!ok) continue;

        double latSec = parts[6].toDouble(&ok);
        if (!ok) continue;

        // 解析高度
        int altitude = parts[7].toInt(&ok);
        if (!ok) continue;

        // 将度分秒转换为弧度
        wp.fLongitude = dmsToRadians(lonDeg, lonMin, lonSec);
        wp.fLatitude = dmsToRadians(latDeg, latMin, latSec);
        wp.shAltitude = altitude;
        waypoints.append(wp);
    }

    file.close();

    //    if(!file.remove()){
    //        emitErrorMessage(QString("[错误] 航点文件删除失败"));
    //    }

    return waypoints;
}

// 将度分秒转换为弧度
double MessageWorker::dmsToRadians(int degrees, int minutes, double seconds)
{
    // 首先转换为十进制度
    double decimalDegrees = degrees + minutes / 60.0 + seconds / 3600.0;

    // 然后转换为弧度（1度 = π/180弧度）
    return decimalDegrees * M_PI / 180.0;
}

void MessageWorker::updateThroughput()
{
    QMutexLocker locker(&mutex);
    
    // 计算平均延迟
    double avgLatency = 0;
    if (messageCount > 0) {
        avgLatency = totalLatency / messageCount;
    }
    
    // 发送性能数据
    emit throughputUpdated(messageCount, packetCount, avgLatency);
    
    // 每秒记录一次性能信息
    emitLogMessage(QString("[性能] 消息吞吐量: %1 消息/秒, %2 数据包/秒, 平均延迟: %3 毫秒")
                   .arg(messageCount)
                   .arg(packetCount)
                   .arg(avgLatency, 0, 'f', 2));
    
    // 重置计数器
    messageCount = 0;
    packetCount = 0;
    totalLatency = 0;
} 
